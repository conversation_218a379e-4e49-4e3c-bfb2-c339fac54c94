<?php
/**
 * Priority 3 Integration System for ChatGABI
 * 
 * This file integrates all Priority 3 enhancements including template translation,
 * cultural context, business terminology, and country-specific features.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_Priority3_Integration {
    
    private $template_translator;
    private $cultural_enhancement;
    private $business_terminology;
    private $country_features;
    
    public function __construct() {
        $this->template_translator = chatgabi_get_enhanced_template_translator();
        $this->cultural_enhancement = chatgabi_get_african_cultural_enhancement();
        $this->business_terminology = chatgabi_get_local_business_terminology();
        $this->country_features = chatgabi_get_country_specific_features();
        
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Template enhancement hooks
        add_filter('chatgabi_template_content', array($this, 'enhance_template_content'), 10, 4);
        add_filter('chatgabi_ai_prompt', array($this, 'enhance_ai_prompt'), 10, 3);
        add_action('chatgabi_template_loaded', array($this, 'add_cultural_context'), 10, 3);
        
        // AJAX handlers for enhanced features
        add_action('wp_ajax_chatgabi_get_enhanced_template', array($this, 'ajax_get_enhanced_template'));
        add_action('wp_ajax_nopriv_chatgabi_get_enhanced_template', array($this, 'ajax_get_enhanced_template'));
        add_action('wp_ajax_chatgabi_get_country_guidance', array($this, 'ajax_get_country_guidance'));
        add_action('wp_ajax_nopriv_chatgabi_get_country_guidance', array($this, 'ajax_get_country_guidance'));
        add_action('wp_ajax_chatgabi_validate_terminology', array($this, 'ajax_validate_terminology'));
        add_action('wp_ajax_nopriv_chatgabi_validate_terminology', array($this, 'ajax_validate_terminology'));
        
        // Localization hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_priority3_assets'));
    }
    
    /**
     * Enhance template content with all Priority 3 features
     */
    public function enhance_template_content($content, $template_id, $language, $country_code) {
        // Get user context
        $user_industry = get_user_meta(get_current_user_id(), 'businesscraft_ai_industry', true);
        $business_stage = get_user_meta(get_current_user_id(), 'businesscraft_ai_business_stage', true);
        
        // Step 1: Apply enhanced template translation
        $enhanced_content = $this->template_translator->translate_template(
            $content, 
            $language, 
            $country_code, 
            $user_industry
        );
        
        // Step 2: Add African cultural context
        $enhanced_content = $this->cultural_enhancement->enhance_business_content(
            $enhanced_content, 
            $country_code, 
            'template_content'
        );
        
        // Step 3: Apply local business terminology
        $enhanced_content = $this->business_terminology->translate_business_text(
            $enhanced_content, 
            'en', 
            $language, 
            'core_business_terms', 
            $country_code
        );
        
        // Step 4: Add country-specific business guidance
        $country_advice = $this->country_features->generate_business_advice(
            $country_code, 
            $business_stage ?: 'startup', 
            $user_industry
        );
        
        $enhanced_content .= $this->format_country_advice($country_advice, $language);
        
        return $enhanced_content;
    }
    
    /**
     * Enhance AI prompts with cultural and linguistic context
     */
    public function enhance_ai_prompt($prompt, $language, $country_code) {
        // Get cultural context
        $cultural_context = $this->cultural_enhancement->get_cultural_context($country_code, 'ai_interaction');
        
        // Add cultural framework to prompt
        if (isset($cultural_context['cultural_framework'])) {
            $framework = $cultural_context['cultural_framework'];
            $prompt .= "\n\nCultural Context: Please consider the " . $framework['primary_philosophy'] . 
                      " philosophy (" . $framework['description'] . ") when providing business advice. " .
                      "Focus on " . $framework['business_application'] . ".";
        }
        
        // Add business terminology context
        $terminology_suggestions = $this->business_terminology->get_terminology_suggestions(
            $language, 
            'general', 
            $country_code
        );
        
        if (!empty($terminology_suggestions['core_business'])) {
            $prompt .= "\n\nLocal Business Terms: Please use appropriate local business terminology such as: " .
                      implode(', ', array_slice($terminology_suggestions['core_business'], 0, 5, true));
        }
        
        // Add country-specific context
        $market_analysis = $this->country_features->get_market_analysis($country_code);
        if (!empty($market_analysis['growth_sectors'])) {
            $prompt .= "\n\nGrowth Sectors: Consider opportunities in these growing sectors: " .
                      implode(', ', array_slice($market_analysis['growth_sectors'], 0, 3));
        }
        
        return $prompt;
    }
    
    /**
     * Add cultural context to loaded templates
     */
    public function add_cultural_context($template_data, $language, $country_code) {
        // This hook allows other plugins/themes to access enhanced template data
        do_action('chatgabi_enhanced_template_loaded', array(
            'template_data' => $template_data,
            'cultural_context' => $this->cultural_enhancement->get_cultural_context($country_code),
            'terminology' => $this->business_terminology->get_localized_terminology($language),
            'country_features' => $this->country_features->get_country_features($country_code)
        ));
    }
    
    /**
     * AJAX handler for getting enhanced templates
     */
    public function ajax_get_enhanced_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_template_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }
        
        $template_id = intval($_POST['template_id']);
        $language = sanitize_text_field($_POST['language']);
        $country_code = sanitize_text_field($_POST['country_code']);
        $industry = sanitize_text_field($_POST['industry']);
        
        try {
            // Get enhanced template
            $enhanced_template = $this->template_translator->get_enhanced_template(
                $template_id, 
                $language, 
                $country_code, 
                $industry
            );
            
            if (!$enhanced_template) {
                wp_send_json_error(__('Template not found', 'chatgabi'));
            }
            
            // Add additional enhancements
            $enhanced_template['cultural_guidance'] = $this->cultural_enhancement->get_cultural_context($country_code);
            $enhanced_template['terminology_suggestions'] = $this->business_terminology->get_terminology_suggestions(
                $language, 
                'general', 
                $country_code
            );
            $enhanced_template['country_advice'] = $this->country_features->generate_business_advice(
                $country_code, 
                'startup', 
                $industry
            );
            
            wp_send_json_success($enhanced_template);
            
        } catch (Exception $e) {
            wp_send_json_error(__('Failed to load enhanced template', 'chatgabi'));
        }
    }
    
    /**
     * AJAX handler for getting country-specific guidance
     */
    public function ajax_get_country_guidance() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_country_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }
        
        $country_code = sanitize_text_field($_POST['country_code']);
        $business_stage = sanitize_text_field($_POST['business_stage']);
        $industry = sanitize_text_field($_POST['industry']);
        
        try {
            $guidance = array(
                'registration_guidance' => $this->country_features->get_registration_guidance($country_code),
                'tax_compliance' => $this->country_features->get_tax_compliance_info($country_code),
                'market_analysis' => $this->country_features->get_market_analysis($country_code),
                'cultural_context' => $this->cultural_enhancement->get_cultural_context($country_code),
                'business_advice' => $this->country_features->generate_business_advice($country_code, $business_stage, $industry)
            );
            
            wp_send_json_success($guidance);
            
        } catch (Exception $e) {
            wp_send_json_error(__('Failed to load country guidance', 'chatgabi'));
        }
    }
    
    /**
     * AJAX handler for terminology validation
     */
    public function ajax_validate_terminology() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_terminology_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }
        
        $content = sanitize_textarea_field($_POST['content']);
        $language = sanitize_text_field($_POST['language']);
        $country_code = sanitize_text_field($_POST['country_code']);
        
        try {
            $validation_results = $this->business_terminology->validate_terminology_usage(
                $content, 
                $language, 
                $country_code
            );
            
            wp_send_json_success($validation_results);
            
        } catch (Exception $e) {
            wp_send_json_error(__('Failed to validate terminology', 'chatgabi'));
        }
    }
    
    /**
     * Enqueue Priority 3 assets
     */
    public function enqueue_priority3_assets() {
        // Localize script with Priority 3 data
        wp_localize_script('chatgabi-templates', 'chatgabiPriority3', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'templateNonce' => wp_create_nonce('chatgabi_template_nonce'),
            'countryNonce' => wp_create_nonce('chatgabi_country_nonce'),
            'terminologyNonce' => wp_create_nonce('chatgabi_terminology_nonce'),
            'currentLanguage' => chatgabi_get_user_preferred_language(),
            'currentCountry' => chatgabi_get_user_country(),
            'strings' => array(
                'loading_template' => __('Loading enhanced template...', 'chatgabi'),
                'loading_guidance' => __('Loading country guidance...', 'chatgabi'),
                'validating_terminology' => __('Validating terminology...', 'chatgabi'),
                'enhancement_complete' => __('Template enhancement complete', 'chatgabi'),
                'cultural_context_added' => __('Cultural context added', 'chatgabi'),
                'terminology_validated' => __('Terminology validated', 'chatgabi'),
                'country_guidance_loaded' => __('Country guidance loaded', 'chatgabi')
            )
        ));
    }
    
    /**
     * Format country advice for display
     */
    private function format_country_advice($advice, $language) {
        $formatted_advice = "\n\n## " . __('Country-Specific Business Guidance', 'chatgabi') . "\n\n";
        
        foreach ($advice as $section_key => $section) {
            if (isset($section['title']) && isset($section['content'])) {
                $formatted_advice .= "### " . $section['title'] . "\n";
                $formatted_advice .= $section['content'] . "\n\n";
                
                // Add additional details if available
                if (isset($section['steps']) && is_array($section['steps'])) {
                    $formatted_advice .= "**" . __('Steps', 'chatgabi') . "**:\n";
                    foreach ($section['steps'] as $step_num => $step_desc) {
                        $formatted_advice .= $step_num . ". " . $step_desc . "\n";
                    }
                    $formatted_advice .= "\n";
                }
                
                if (isset($section['opportunities']) && is_array($section['opportunities'])) {
                    $formatted_advice .= "**" . __('Opportunities', 'chatgabi') . "**:\n";
                    foreach ($section['opportunities'] as $opportunity) {
                        $formatted_advice .= "- " . $opportunity . "\n";
                    }
                    $formatted_advice .= "\n";
                }
                
                if (isset($section['incentives']) && is_array($section['incentives'])) {
                    $formatted_advice .= "**" . __('Incentives', 'chatgabi') . "**:\n";
                    foreach ($section['incentives'] as $incentive) {
                        $formatted_advice .= "- " . $incentive . "\n";
                    }
                    $formatted_advice .= "\n";
                }
            }
        }
        
        return $formatted_advice;
    }
    
    /**
     * Get comprehensive business intelligence for a country
     */
    public function get_comprehensive_business_intelligence($country_code, $language, $industry = null) {
        return array(
            'cultural_framework' => $this->cultural_enhancement->get_cultural_context($country_code),
            'business_terminology' => $this->business_terminology->get_localized_terminology($language, 'core_business_terms', $industry),
            'country_features' => $this->country_features->get_country_features($country_code),
            'market_analysis' => $this->country_features->get_market_analysis($country_code),
            'registration_guidance' => $this->country_features->get_registration_guidance($country_code),
            'tax_compliance' => $this->country_features->get_tax_compliance_info($country_code),
            'terminology_suggestions' => $this->business_terminology->get_terminology_suggestions($language, 'general', $country_code)
        );
    }
    
    /**
     * Generate localized business plan template
     */
    public function generate_localized_business_plan($country_code, $language, $industry, $business_stage) {
        // Get base business plan template
        $base_template = $this->get_base_business_plan_template();
        
        // Apply all enhancements
        $localized_plan = $this->enhance_template_content($base_template, 0, $language, $country_code);
        
        // Add industry-specific sections
        if ($industry) {
            $industry_vocabulary = $this->business_terminology->get_industry_vocabulary($industry, $language);
            $localized_plan .= $this->generate_industry_specific_sections($industry, $country_code, $language);
        }
        
        return $localized_plan;
    }
    
    /**
     * Get base business plan template
     */
    private function get_base_business_plan_template() {
        return "# Business Plan Template\n\n" .
               "## Executive Summary\n" .
               "Provide a brief overview of your business concept, target market, and financial projections.\n\n" .
               "## Business Description\n" .
               "Describe your business, products/services, and value proposition.\n\n" .
               "## Market Analysis\n" .
               "Analyze your target market, competition, and market opportunities.\n\n" .
               "## Organization & Management\n" .
               "Outline your business structure and management team.\n\n" .
               "## Marketing & Sales Strategy\n" .
               "Describe how you will attract and retain customers.\n\n" .
               "## Financial Projections\n" .
               "Provide detailed financial forecasts and funding requirements.\n\n";
    }
    
    /**
     * Generate industry-specific sections
     */
    private function generate_industry_specific_sections($industry, $country_code, $language) {
        $sections = "\n\n## " . sprintf(__('Industry-Specific Considerations for %s', 'chatgabi'), ucfirst($industry)) . "\n\n";
        
        // Get industry vocabulary
        $vocabulary = $this->business_terminology->get_industry_vocabulary($industry, $language);
        
        if (!empty($vocabulary)) {
            $sections .= "### " . __('Key Industry Terms', 'chatgabi') . "\n";
            foreach (array_slice($vocabulary, 0, 10, true) as $english_term => $local_term) {
                $sections .= "- **" . ucfirst(str_replace('_', ' ', $english_term)) . "**: " . $local_term . "\n";
            }
            $sections .= "\n";
        }
        
        // Add country-specific industry insights
        $market_analysis = $this->country_features->get_market_analysis($country_code);
        if (in_array(ucfirst($industry), $market_analysis['growth_sectors'] ?? array())) {
            $sections .= "### " . __('Growth Sector Opportunity', 'chatgabi') . "\n";
            $sections .= sprintf(__('%s is identified as a growth sector in %s, presenting significant opportunities for new businesses.', 'chatgabi'), 
                               ucfirst($industry), 
                               $this->get_country_name($country_code)) . "\n\n";
        }
        
        return $sections;
    }
    
    /**
     * Get country name from code
     */
    private function get_country_name($country_code) {
        $countries = array(
            'GH' => __('Ghana', 'chatgabi'),
            'KE' => __('Kenya', 'chatgabi'),
            'NG' => __('Nigeria', 'chatgabi'),
            'ZA' => __('South Africa', 'chatgabi')
        );
        return $countries[$country_code] ?? __('Ghana', 'chatgabi');
    }
}

// Initialize the Priority 3 integration system
function chatgabi_get_priority3_integration() {
    static $integration = null;
    
    if ($integration === null) {
        $integration = new ChatGABI_Priority3_Integration();
    }
    
    return $integration;
}

// Initialize on WordPress init
add_action('init', function() {
    chatgabi_get_priority3_integration();
});
?>
