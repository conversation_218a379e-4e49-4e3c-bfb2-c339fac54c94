<?php
/**
 * African Theme Customizer for ChatGABI
 * Cultural visual elements and African-inspired design options
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add African customization options to WordPress Customizer
 */
function chatgabi_customize_register($wp_customize) {
    
    // Add African Market Section
    $wp_customize->add_section('chatgabi_african_market', array(
        'title' => __('African Market Customization', 'chatgabi'),
        'description' => __('Customize ChatGABI with African cultural elements and regional preferences.', 'chatgabi'),
        'priority' => 30,
    ));

    // Color Scheme Selection
    $wp_customize->add_setting('chatgabi_color_scheme', array(
        'default' => 'default',
        'sanitize_callback' => 'chatgabi_sanitize_color_scheme',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_color_scheme', array(
        'label' => __('African Color Scheme', 'chatgabi'),
        'description' => __('Choose an African-inspired color palette for your site.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'select',
        'choices' => array(
            'default' => __('Default Blue', 'chatgabi'),
            'ghana_gold' => __('Ghana Gold & Green', 'chatgabi'),
            'kenya_red' => __('Kenya Red & Black', 'chatgabi'),
            'nigeria_green' => __('Nigeria Green & White', 'chatgabi'),
            'south_africa_rainbow' => __('South Africa Rainbow', 'chatgabi'),
            'earth_tones' => __('African Earth Tones', 'chatgabi'),
            'sunset_orange' => __('African Sunset', 'chatgabi'),
        ),
    ));

    // Cultural Patterns
    $wp_customize->add_setting('chatgabi_cultural_patterns', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_cultural_patterns', array(
        'label' => __('Enable Cultural Patterns', 'chatgabi'),
        'description' => __('Add subtle African-inspired patterns to backgrounds and borders.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'checkbox',
    ));

    // Typography Enhancement
    $wp_customize->add_setting('chatgabi_african_typography', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_african_typography', array(
        'label' => __('African Typography Enhancement', 'chatgabi'),
        'description' => __('Use fonts that support African language characters and cultural aesthetics.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'checkbox',
    ));

    // Business Imagery
    $wp_customize->add_setting('chatgabi_african_imagery', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_african_imagery', array(
        'label' => __('African Business Imagery', 'chatgabi'),
        'description' => __('Show African business and entrepreneurship imagery throughout the site.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'checkbox',
    ));

    // Regional Preferences
    $wp_customize->add_setting('chatgabi_primary_region', array(
        'default' => 'multi',
        'sanitize_callback' => 'chatgabi_sanitize_region',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_primary_region', array(
        'label' => __('Primary Region Focus', 'chatgabi'),
        'description' => __('Emphasize content and examples from a specific African region.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'select',
        'choices' => array(
            'multi' => __('Multi-Regional (All Countries)', 'chatgabi'),
            'GH' => __('Ghana Focus', 'chatgabi'),
            'KE' => __('Kenya Focus', 'chatgabi'),
            'NG' => __('Nigeria Focus', 'chatgabi'),
            'ZA' => __('South Africa Focus', 'chatgabi'),
        ),
    ));

    // Cultural Sensitivity Mode
    $wp_customize->add_setting('chatgabi_cultural_sensitivity', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_cultural_sensitivity', array(
        'label' => __('Cultural Sensitivity Mode', 'chatgabi'),
        'description' => __('Enable enhanced cultural awareness in AI responses and content.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'checkbox',
    ));

    // Ubuntu Philosophy Integration
    $wp_customize->add_setting('chatgabi_ubuntu_philosophy', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('chatgabi_ubuntu_philosophy', array(
        'label' => __('Ubuntu Philosophy Integration', 'chatgabi'),
        'description' => __('Emphasize community, interconnectedness, and collective success in business advice.', 'chatgabi'),
        'section' => 'chatgabi_african_market',
        'type' => 'checkbox',
    ));
}
add_action('customize_register', 'chatgabi_customize_register');

/**
 * Sanitize color scheme selection
 */
function chatgabi_sanitize_color_scheme($input) {
    $valid_schemes = array(
        'default', 'ghana_gold', 'kenya_red', 'nigeria_green', 
        'south_africa_rainbow', 'earth_tones', 'sunset_orange'
    );
    
    return in_array($input, $valid_schemes) ? $input : 'default';
}

/**
 * Sanitize region selection
 */
function chatgabi_sanitize_region($input) {
    $valid_regions = array('multi', 'GH', 'KE', 'NG', 'ZA');
    return in_array($input, $valid_regions) ? $input : 'multi';
}

/**
 * Generate CSS for African color schemes
 */
function chatgabi_get_color_scheme_css($scheme) {
    $css = '';
    
    switch ($scheme) {
        case 'ghana_gold':
            $css = "
                :root {
                    --primary-color: #FFD700;
                    --secondary-color: #228B22;
                    --accent-color: #DC143C;
                    --text-color: #2F4F2F;
                    --background-color: #FFFEF7;
                    --border-color: #DAA520;
                }
                .btn-primary { background-color: var(--primary-color); border-color: var(--border-color); color: var(--text-color); }
                .navbar { background-color: var(--secondary-color); }
                .card { border-left: 4px solid var(--primary-color); }
            ";
            break;
            
        case 'kenya_red':
            $css = "
                :root {
                    --primary-color: #DC143C;
                    --secondary-color: #000000;
                    --accent-color: #228B22;
                    --text-color: #2F2F2F;
                    --background-color: #FFFFFF;
                    --border-color: #8B0000;
                }
                .btn-primary { background-color: var(--primary-color); border-color: var(--border-color); }
                .navbar { background-color: var(--secondary-color); }
                .card { border-left: 4px solid var(--primary-color); }
            ";
            break;
            
        case 'nigeria_green':
            $css = "
                :root {
                    --primary-color: #228B22;
                    --secondary-color: #FFFFFF;
                    --accent-color: #FFD700;
                    --text-color: #2F4F2F;
                    --background-color: #F8FFF8;
                    --border-color: #006400;
                }
                .btn-primary { background-color: var(--primary-color); border-color: var(--border-color); }
                .navbar { background-color: var(--primary-color); }
                .card { border-left: 4px solid var(--accent-color); }
            ";
            break;
            
        case 'south_africa_rainbow':
            $css = "
                :root {
                    --primary-color: #007A4D;
                    --secondary-color: #FFB612;
                    --accent-color: #DE3831;
                    --text-color: #2F2F2F;
                    --background-color: #FFFFFF;
                    --border-color: #002F6C;
                }
                .btn-primary { background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); border: none; }
                .navbar { background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color)); }
                .card { border-left: 4px solid var(--primary-color); }
            ";
            break;
            
        case 'earth_tones':
            $css = "
                :root {
                    --primary-color: #8B4513;
                    --secondary-color: #D2691E;
                    --accent-color: #CD853F;
                    --text-color: #654321;
                    --background-color: #FFF8DC;
                    --border-color: #A0522D;
                }
                .btn-primary { background-color: var(--primary-color); border-color: var(--border-color); }
                .navbar { background-color: var(--secondary-color); }
                .card { border-left: 4px solid var(--accent-color); }
            ";
            break;
            
        case 'sunset_orange':
            $css = "
                :root {
                    --primary-color: #FF6347;
                    --secondary-color: #FF8C00;
                    --accent-color: #FFD700;
                    --text-color: #8B4513;
                    --background-color: #FFF8F0;
                    --border-color: #FF4500;
                }
                .btn-primary { background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); border: none; }
                .navbar { background-color: var(--primary-color); }
                .card { border-left: 4px solid var(--accent-color); }
            ";
            break;
    }
    
    return $css;
}

/**
 * Generate CSS for cultural patterns
 */
function chatgabi_get_cultural_patterns_css() {
    return "
        .african-pattern-bg {
            background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"%23000\" opacity=\"0.1\"/><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"%23000\" opacity=\"0.05\"/><circle cx=\"30\" cy=\"30\" r=\"1\" fill=\"%23000\" opacity=\"0.05\"/></svg>');
        }
        
        .african-border {
            border-image: linear-gradient(45deg, var(--primary-color), var(--accent-color)) 1;
        }
        
        .cultural-divider {
            height: 3px;
            background: repeating-linear-gradient(
                90deg,
                var(--primary-color) 0px,
                var(--primary-color) 10px,
                var(--accent-color) 10px,
                var(--accent-color) 20px
            );
            margin: 20px 0;
        }
    ";
}

/**
 * Output customizer CSS
 */
function chatgabi_customizer_css() {
    $color_scheme = get_theme_mod('chatgabi_color_scheme', 'default');
    $cultural_patterns = get_theme_mod('chatgabi_cultural_patterns', false);
    
    $css = '';
    
    if ($color_scheme !== 'default') {
        $css .= chatgabi_get_color_scheme_css($color_scheme);
    }
    
    if ($cultural_patterns) {
        $css .= chatgabi_get_cultural_patterns_css();
    }
    
    if (!empty($css)) {
        echo '<style type="text/css" id="chatgabi-customizer-css">' . $css . '</style>';
    }
}
add_action('wp_head', 'chatgabi_customizer_css');

/**
 * Get African typography fonts
 */
function chatgabi_get_african_typography_fonts() {
    $african_typography = get_theme_mod('chatgabi_african_typography', false);
    
    if ($african_typography) {
        // Enqueue Google Fonts that support African languages
        wp_enqueue_style(
            'chatgabi-african-fonts',
            'https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300;400;600;700&family=Ubuntu:wght@300;400;500;700&display=swap',
            array(),
            '1.0.0'
        );
        
        // Add CSS for African typography
        $typography_css = "
            body, .chatgabi-main {
                font-family: 'Ubuntu', 'Noto Sans', sans-serif;
            }
            
            h1, h2, h3, h4, h5, h6 {
                font-family: 'Ubuntu', sans-serif;
                font-weight: 500;
            }
            
            .african-text {
                font-family: 'Noto Sans', sans-serif;
                line-height: 1.6;
            }
        ";
        
        wp_add_inline_style('chatgabi-african-fonts', $typography_css);
    }
}
add_action('wp_enqueue_scripts', 'chatgabi_get_african_typography_fonts');
