/**
 * ChatGABI Language Switcher JavaScript
 * 
 * Handles real-time language switching with AJAX and UI updates
 */

// Import WordPress i18n functions
const { __ } = wp.i18n;

/**
 * Language Switcher Class
 */
class ChatGABILanguageSwitcher {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentLanguage = chatgabiLanguage.currentLanguage || 'en';
        this.supportedLanguages = chatgabiLanguage.supportedLanguages || {};
        this.strings = chatgabiLanguage.strings || {};
        
        if (this.container) {
            this.init();
        }
    }
    
    init() {
        this.bindEvents();
        this.updateDirection();
        this.loadLanguageStrings();
    }
    
    bindEvents() {
        // Handle dropdown changes
        const select = this.container.querySelector('.chatgabi-language-select');
        if (select) {
            select.addEventListener('change', (e) => {
                this.switchLanguage(e.target.value);
            });
        }
        
        // Handle button clicks
        const buttons = this.container.querySelectorAll('.chatgabi-language-btn');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const language = button.getAttribute('data-language');
                this.switchLanguage(language);
            });
        });
        
        // Handle minimal style clicks
        const options = this.container.querySelectorAll('.language-option');
        options.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const language = option.getAttribute('data-language');
                this.switchLanguage(language);
            });
        });
        
        // Handle minimal dropdown toggle
        const currentLang = this.container.querySelector('.current-language');
        if (currentLang) {
            currentLang.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleMinimalDropdown();
            });
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.closeMinimalDropdown();
            }
        });
    }
    
    async switchLanguage(newLanguage) {
        if (newLanguage === this.currentLanguage) {
            return;
        }
        
        try {
            this.showLoading();
            
            // Save preference via AJAX
            const response = await this.saveLanguagePreference(newLanguage);
            
            if (response.success) {
                // Update current language
                this.currentLanguage = newLanguage;
                
                // Update UI strings
                await this.updateUIStrings(response.data.strings);
                
                // Update form elements
                this.updateFormElements(newLanguage);
                
                // Update direction
                this.updateDirection();
                
                // Update switcher UI
                this.updateSwitcherUI(newLanguage);
                
                // Update currency display if available
                this.updateCurrencyDisplay(newLanguage);
                
                // Trigger custom event
                this.triggerLanguageChangeEvent(newLanguage, response.data);
                
                // Show success message
                this.showMessage(response.data.message || __('Language switched successfully', 'chatgabi'), 'success');
                
            } else {
                throw new Error(response.data || __('Failed to switch language', 'chatgabi'));
            }
            
        } catch (error) {
            console.error('Language switch failed:', error);
            this.showMessage(__('Failed to switch language. Please try again.', 'chatgabi'), 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async saveLanguagePreference(language) {
        const formData = new FormData();
        formData.append('action', 'chatgabi_switch_language');
        formData.append('language', language);
        formData.append('nonce', chatgabiLanguage.nonce);
        
        const response = await fetch(chatgabiLanguage.ajaxUrl, {
            method: 'POST',
            body: formData
        });
        
        return await response.json();
    }
    
    async updateUIStrings(newStrings) {
        // Update all elements with data-translate attribute
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            if (newStrings[key]) {
                if (element.tagName === 'INPUT' && element.type === 'submit') {
                    element.value = newStrings[key];
                } else if (element.tagName === 'INPUT' && element.hasAttribute('placeholder')) {
                    element.placeholder = newStrings[key];
                } else {
                    element.textContent = newStrings[key];
                }
            }
        });
        
        // Update specific UI elements
        this.updateSpecificElements(newStrings);
        
        // Store strings for future use
        this.strings = newStrings;
    }
    
    updateSpecificElements(strings) {
        // Update chat submit button
        const chatSubmit = document.getElementById('chat-submit');
        if (chatSubmit && strings.send_message) {
            chatSubmit.textContent = strings.send_message;
        }
        
        // Update template search placeholder
        const templateSearch = document.querySelector('#template-search, .template-search');
        if (templateSearch && strings.search_templates) {
            templateSearch.placeholder = strings.search_templates;
        }
        
        // Update template selector
        const templateSelect = document.getElementById('prompt-templates');
        if (templateSelect && strings.select_template) {
            const firstOption = templateSelect.querySelector('option[value=""]');
            if (firstOption) {
                firstOption.textContent = strings.select_template;
            }
        }
        
        // Update credit display
        const creditDisplay = document.querySelector('.credit-display');
        if (creditDisplay && strings.credits) {
            const creditText = creditDisplay.textContent;
            const creditNumber = creditText.match(/\d+/);
            if (creditNumber) {
                creditDisplay.textContent = strings.credits.replace('%d', creditNumber[0]);
            }
        }
    }
    
    updateFormElements(language) {
        // Update placeholders based on language
        const placeholders = {
            'en': {
                'chat-input': __('Ask me to help with your business needs...', 'chatgabi'),
                'template-search': __('Search templates...', 'chatgabi'),
                'business-name': __('Enter your business name...', 'chatgabi')
            },
            'sw': {
                'chat-input': 'Niulize nisaidie mahitaji ya biashara yako...',
                'template-search': 'Tafuta violezo...',
                'business-name': 'Ingiza jina la biashara yako...'
            },
            'yo': {
                'chat-input': 'Beere lọwọ mi lati ṣe iranlọwọ pẹlu awọn iwulo iṣowo rẹ...',
                'template-search': 'Wa awọn awoṣe...',
                'business-name': 'Tẹ orukọ iṣowo rẹ sinu...'
            },
            'tw': {
                'chat-input': 'Srɛ me sɛ mmmoa wo wɔ w\'adwuma ahiade ho...',
                'template-search': 'Hwehwɛ nhwɛsoɔ ahorow...',
                'business-name': 'Hyɛ w\'adwuma din...'
            },
            'zu': {
                'chat-input': 'Ngibuze ukuthi ngikusize ngezidingo zakho zebhizinisi...',
                'template-search': 'Sesha izifanekiso...',
                'business-name': 'Faka igama lebhizinisi lakho...'
            }
        };
        
        const langPlaceholders = placeholders[language] || placeholders['en'];
        
        Object.entries(langPlaceholders).forEach(([id, text]) => {
            const element = document.getElementById(id);
            if (element) {
                element.placeholder = text;
            }
        });
    }
    
    updateDirection() {
        const languageData = this.supportedLanguages[this.currentLanguage];
        if (languageData && languageData.direction) {
            document.documentElement.setAttribute('dir', languageData.direction);
            document.body.classList.toggle('rtl', languageData.direction === 'rtl');
        }
    }
    
    updateSwitcherUI(newLanguage) {
        // Update dropdown selection
        const select = this.container.querySelector('.chatgabi-language-select');
        if (select) {
            select.value = newLanguage;
        }
        
        // Update button states
        const buttons = this.container.querySelectorAll('.chatgabi-language-btn');
        buttons.forEach(button => {
            const isActive = button.getAttribute('data-language') === newLanguage;
            button.classList.toggle('active', isActive);
        });
        
        // Update minimal display
        const currentLang = this.container.querySelector('.current-language');
        if (currentLang) {
            const languageData = this.supportedLanguages[newLanguage];
            if (languageData) {
                const showFlags = this.container.classList.contains('show-flags');
                const showNative = this.container.classList.contains('show-native');
                
                let displayText = '';
                if (showFlags) {
                    displayText += languageData.flag + ' ';
                }
                displayText += showNative ? languageData.native_name : languageData.name;
                
                currentLang.textContent = displayText;
                currentLang.setAttribute('data-language', newLanguage);
            }
        }
    }
    
    updateCurrencyDisplay(language) {
        // Update currency displays if currency formatter is available
        if (typeof paystackConfig !== 'undefined' && paystackConfig.currencies) {
            const countryMap = {
                'en': 'US',
                'sw': 'KE',
                'yo': 'NG', 
                'tw': 'GH',
                'zu': 'ZA'
            };
            
            const country = countryMap[language] || 'GH';
            const currency = paystackConfig.currencies[country];
            
            if (currency) {
                // Update all currency displays
                document.querySelectorAll('.currency-display, .price-display').forEach(element => {
                    const amount = parseFloat(element.getAttribute('data-amount'));
                    if (!isNaN(amount)) {
                        element.textContent = this.formatCurrency(amount, currency);
                    }
                });
            }
        }
    }
    
    formatCurrency(amount, currency) {
        const formatted = amount.toLocaleString('en-US', {
            minimumFractionDigits: currency.decimalPlaces,
            maximumFractionDigits: currency.decimalPlaces
        });
        
        const space = currency.spaceBetween ? ' ' : '';
        
        if (currency.position === 'before') {
            return currency.symbol + space + formatted;
        } else {
            return formatted + space + currency.symbol;
        }
    }
    
    toggleMinimalDropdown() {
        const dropdown = this.container.querySelector('.language-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }
    
    closeMinimalDropdown() {
        const dropdown = this.container.querySelector('.language-dropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }
    
    showLoading() {
        this.container.classList.add('loading');
        
        // Disable all interactive elements
        const interactiveElements = this.container.querySelectorAll('select, button, a');
        interactiveElements.forEach(element => {
            element.disabled = true;
        });
    }
    
    hideLoading() {
        this.container.classList.remove('loading');
        
        // Re-enable all interactive elements
        const interactiveElements = this.container.querySelectorAll('select, button, a');
        interactiveElements.forEach(element => {
            element.disabled = false;
        });
    }
    
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.chatgabi-language-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'chatgabi-language-message';
            this.container.appendChild(messageEl);
        }
        
        messageEl.textContent = message;
        messageEl.className = `chatgabi-language-message ${type}`;
        messageEl.style.display = 'block';
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 3000);
    }
    
    triggerLanguageChangeEvent(language, data) {
        const event = new CustomEvent('chatgabi:languageChanged', {
            detail: {
                language: language,
                languageData: data.language_data,
                strings: data.strings
            }
        });
        
        document.dispatchEvent(event);
    }
    
    async loadLanguageStrings() {
        // Load additional strings if needed
        if (Object.keys(this.strings).length === 0) {
            try {
                const response = await fetch(`${chatgabiLanguage.ajaxUrl}?action=chatgabi_get_language_strings&language=${this.currentLanguage}&nonce=${chatgabiLanguage.nonce}`);
                const data = await response.json();
                
                if (data.success) {
                    this.strings = data.data;
                }
            } catch (error) {
                console.warn('Failed to load language strings:', error);
            }
        }
    }
}

/**
 * Initialize language switcher
 */
function chatgabiInitLanguageSwitcher(containerId) {
    return new ChatGABILanguageSwitcher(containerId);
}

/**
 * Global language switching function
 */
function chatgabiSwitchLanguage(language) {
    const switchers = document.querySelectorAll('.chatgabi-language-switcher');
    if (switchers.length > 0) {
        const firstSwitcher = switchers[0];
        const switcherInstance = firstSwitcher._chatgabiSwitcher;
        if (switcherInstance) {
            switcherInstance.switchLanguage(language);
        }
    }
}

// Auto-initialize language switchers on page load
document.addEventListener('DOMContentLoaded', function() {
    const switchers = document.querySelectorAll('.chatgabi-language-switcher');
    switchers.forEach((switcher, index) => {
        const id = switcher.id || `chatgabi-language-switcher-${index}`;
        switcher.id = id;
        switcher._chatgabiSwitcher = chatgabiInitLanguageSwitcher(id);
    });
});

// Make functions globally available
window.chatgabiInitLanguageSwitcher = chatgabiInitLanguageSwitcher;
window.chatgabiSwitchLanguage = chatgabiSwitchLanguage;
