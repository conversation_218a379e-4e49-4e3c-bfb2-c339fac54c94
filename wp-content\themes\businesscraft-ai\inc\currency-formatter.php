<?php
/**
 * Currency Formatting for African Markets
 * 
 * This file provides comprehensive currency formatting functionality
 * for ChatGABI's African market support (Ghana, Kenya, Nigeria, South Africa)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get currency data for African countries
 * 
 * @param string $country_code Two-letter country code
 * @return array Currency configuration
 */
function chatgabi_get_currency_data($country_code = null) {
    // If no country code provided, try to detect user's country
    if (!$country_code) {
        $country_code = chatgabi_get_user_country();
    }
    
    $currencies = array(
        'GH' => array(
            'name' => __('Ghanaian Cedi', 'chatgabi'),
            'symbol' => 'GH₵',
            'code' => 'GHS',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'space_between' => false,
            'exchange_rate_usd' => 12.50, // Approximate rate - should be updated from API
            'paystack_supported' => true,
            'local_name' => __('Cedi', 'chatgabi')
        ),
        'KE' => array(
            'name' => __('Kenyan Shilling', 'chatgabi'),
            'symbol' => 'KSh',
            'code' => 'KES',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'space_between' => true,
            'exchange_rate_usd' => 130.00, // Approximate rate
            'paystack_supported' => true,
            'local_name' => __('Shilling', 'chatgabi')
        ),
        'NG' => array(
            'name' => __('Nigerian Naira', 'chatgabi'),
            'symbol' => '₦',
            'code' => 'NGN',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'space_between' => false,
            'exchange_rate_usd' => 800.00, // Approximate rate
            'paystack_supported' => true,
            'local_name' => __('Naira', 'chatgabi')
        ),
        'ZA' => array(
            'name' => __('South African Rand', 'chatgabi'),
            'symbol' => 'R',
            'code' => 'ZAR',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'space_between' => true,
            'exchange_rate_usd' => 18.50, // Approximate rate
            'paystack_supported' => true,
            'local_name' => __('Rand', 'chatgabi')
        ),
        'US' => array(
            'name' => __('US Dollar', 'chatgabi'),
            'symbol' => '$',
            'code' => 'USD',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'space_between' => false,
            'exchange_rate_usd' => 1.00,
            'paystack_supported' => false,
            'local_name' => __('Dollar', 'chatgabi')
        )
    );
    
    return $currencies[$country_code] ?? $currencies['GH']; // Default to Ghana
}

/**
 * Get user's country code
 * 
 * @return string Two-letter country code
 */
function chatgabi_get_user_country() {
    $user_id = get_current_user_id();
    
    if ($user_id) {
        $user_country = get_user_meta($user_id, 'businesscraft_ai_country', true);
        if ($user_country) {
            return $user_country;
        }
    }
    
    // Try to detect from IP or other methods
    if (function_exists('businesscraft_ai_get_user_country')) {
        return businesscraft_ai_get_user_country();
    }
    
    // Default to Ghana
    return 'GH';
}

/**
 * Format currency amount for display
 * 
 * @param float $amount Amount to format
 * @param string $country_code Country code for currency
 * @param bool $include_code Whether to include currency code
 * @return string Formatted currency string
 */
function chatgabi_format_currency($amount, $country_code = null, $include_code = false) {
    $currency = chatgabi_get_currency_data($country_code);
    
    // Format the number
    $formatted_amount = number_format(
        $amount,
        $currency['decimal_places'],
        $currency['decimal_separator'],
        $currency['thousands_separator']
    );
    
    // Build the currency string
    $space = $currency['space_between'] ? ' ' : '';
    
    if ($currency['position'] === 'before') {
        $result = $currency['symbol'] . $space . $formatted_amount;
    } else {
        $result = $formatted_amount . $space . $currency['symbol'];
    }
    
    // Add currency code if requested
    if ($include_code) {
        $result .= ' (' . $currency['code'] . ')';
    }
    
    return $result;
}

/**
 * Convert USD amount to local currency
 * 
 * @param float $usd_amount Amount in USD
 * @param string $country_code Target country code
 * @return float Amount in local currency
 */
function chatgabi_convert_from_usd($usd_amount, $country_code = null) {
    $currency = chatgabi_get_currency_data($country_code);
    return $usd_amount * $currency['exchange_rate_usd'];
}

/**
 * Convert local currency to USD
 * 
 * @param float $local_amount Amount in local currency
 * @param string $country_code Source country code
 * @return float Amount in USD
 */
function chatgabi_convert_to_usd($local_amount, $country_code = null) {
    $currency = chatgabi_get_currency_data($country_code);
    return $local_amount / $currency['exchange_rate_usd'];
}

/**
 * Get all supported currencies
 * 
 * @return array Array of all currency configurations
 */
function chatgabi_get_all_currencies() {
    return array(
        'GH' => chatgabi_get_currency_data('GH'),
        'KE' => chatgabi_get_currency_data('KE'),
        'NG' => chatgabi_get_currency_data('NG'),
        'ZA' => chatgabi_get_currency_data('ZA'),
        'US' => chatgabi_get_currency_data('US')
    );
}

/**
 * Get currency selector HTML for forms
 * 
 * @param string $selected_country Currently selected country
 * @param string $field_name Form field name
 * @return string HTML for currency selector
 */
function chatgabi_get_currency_selector($selected_country = null, $field_name = 'currency_country') {
    if (!$selected_country) {
        $selected_country = chatgabi_get_user_country();
    }
    
    $currencies = chatgabi_get_all_currencies();
    
    ob_start();
    ?>
    <select name="<?php echo esc_attr($field_name); ?>" id="<?php echo esc_attr($field_name); ?>" class="chatgabi-currency-selector">
        <?php foreach ($currencies as $country_code => $currency): ?>
            <option value="<?php echo esc_attr($country_code); ?>" 
                    <?php selected($selected_country, $country_code); ?>
                    data-symbol="<?php echo esc_attr($currency['symbol']); ?>"
                    data-code="<?php echo esc_attr($currency['code']); ?>">
                <?php echo esc_html($currency['symbol'] . ' - ' . $currency['name']); ?>
            </option>
        <?php endforeach; ?>
    </select>
    <?php
    return ob_get_clean();
}

/**
 * Get JavaScript currency data for frontend
 * 
 * @return array Currency data formatted for JavaScript
 */
function chatgabi_get_js_currency_data() {
    $currencies = chatgabi_get_all_currencies();
    $js_data = array();
    
    foreach ($currencies as $country_code => $currency) {
        $js_data[$country_code] = array(
            'symbol' => $currency['symbol'],
            'code' => $currency['code'],
            'name' => $currency['name'],
            'position' => $currency['position'],
            'decimalPlaces' => $currency['decimal_places'],
            'thousandsSeparator' => $currency['thousands_separator'],
            'decimalSeparator' => $currency['decimal_separator'],
            'spaceBetween' => $currency['space_between'],
            'exchangeRateUsd' => $currency['exchange_rate_usd']
        );
    }
    
    return $js_data;
}

/**
 * Update exchange rates from external API
 * 
 * @return bool Success status
 */
function chatgabi_update_exchange_rates() {
    // This would typically fetch from a currency API
    // For now, we'll use static rates but this can be enhanced
    
    $rates = array(
        'GHS' => 12.50,
        'KES' => 130.00,
        'NGN' => 800.00,
        'ZAR' => 18.50
    );
    
    update_option('chatgabi_exchange_rates', $rates);
    update_option('chatgabi_exchange_rates_updated', time());
    
    return true;
}

/**
 * Get credit package prices in local currency
 * 
 * @param string $country_code Country code
 * @return array Package prices in local currency
 */
function chatgabi_get_localized_credit_packages($country_code = null) {
    // Base prices in USD
    $base_packages = array(
        'starter' => array(
            'name' => __('Starter Pack', 'chatgabi'),
            'credits' => 500,
            'price_usd' => 5.00,
            'features' => array(
                __('500 AI Credits', 'chatgabi'),
                __('Basic Templates', 'chatgabi'),
                __('Email Support', 'chatgabi')
            )
        ),
        'growth' => array(
            'name' => __('Growth Pack', 'chatgabi'),
            'credits' => 1500,
            'price_usd' => 15.00,
            'features' => array(
                __('1,500 AI Credits', 'chatgabi'),
                __('GPT-4 Access', 'chatgabi'),
                __('Premium Templates', 'chatgabi'),
                __('Priority Support', 'chatgabi')
            )
        ),
        'business' => array(
            'name' => __('Business Pack', 'chatgabi'),
            'credits' => 3000,
            'price_usd' => 30.00,
            'features' => array(
                __('3,000 AI Credits', 'chatgabi'),
                __('GPT-4 Access', 'chatgabi'),
                __('All Templates', 'chatgabi'),
                __('Custom Templates', 'chatgabi'),
                __('Phone Support', 'chatgabi')
            )
        )
    );
    
    $currency = chatgabi_get_currency_data($country_code);
    $localized_packages = array();
    
    foreach ($base_packages as $package_id => $package) {
        $local_price = chatgabi_convert_from_usd($package['price_usd'], $country_code);
        
        $localized_packages[$package_id] = array_merge($package, array(
            'price_local' => $local_price,
            'price_formatted' => chatgabi_format_currency($local_price, $country_code),
            'currency_code' => $currency['code'],
            'currency_symbol' => $currency['symbol']
        ));
    }
    
    return $localized_packages;
}

// Initialize currency system
add_action('init', function() {
    // Update exchange rates daily
    if (!wp_next_scheduled('chatgabi_update_exchange_rates')) {
        wp_schedule_event(time(), 'daily', 'chatgabi_update_exchange_rates');
    }
});

add_action('chatgabi_update_exchange_rates', 'chatgabi_update_exchange_rates');
?>
