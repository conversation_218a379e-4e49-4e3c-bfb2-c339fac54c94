<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI AI Widget Frontend Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #fff3cd; color: #856404; }
        .ai-suggestions { background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .suggestion-item { padding: 5px; margin: 2px 0; background: white; border-radius: 3px; cursor: pointer; }
        .suggestion-item:hover { background: #f0f8ff; }
    </style>
</head>
<body>

<div class="container">
    <h1>🤖 ChatGABI AI Widget Frontend Integration Test</h1>
    <p>Test the AI widget functionality with real frontend interactions.</p>
    
    <!-- Test 1: AI Field Suggestions -->
    <div class="test-section">
        <h2>Test 1: AI Field Suggestions</h2>
        <div class="form-group">
            <label for="field-type">Field Type:</label>
            <select id="field-type">
                <option value="business_description">Business Description</option>
                <option value="market_analysis">Market Analysis</option>
                <option value="financial_projections">Financial Projections</option>
                <option value="competitive_analysis">Competitive Analysis</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="field-content">Current Content:</label>
            <textarea id="field-content" rows="3" placeholder="Enter some content to get AI suggestions..."></textarea>
        </div>
        
        <div class="form-group">
            <label for="user-country">Country:</label>
            <select id="user-country">
                <option value="GH">Ghana</option>
                <option value="KE">Kenya</option>
                <option value="NG">Nigeria</option>
                <option value="ZA">South Africa</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="user-industry">Industry:</label>
            <select id="user-industry">
                <option value="technology">Technology</option>
                <option value="agriculture">Agriculture</option>
                <option value="finance">Finance</option>
                <option value="healthcare">Healthcare</option>
                <option value="education">Education</option>
            </select>
        </div>
        
        <button onclick="getSuggestions()" id="suggestions-btn">Get AI Suggestions</button>
        <div id="suggestions-result" class="result" style="display: none;"></div>
        <div id="suggestions-display" class="ai-suggestions" style="display: none;"></div>
    </div>
    
    <!-- Test 2: Content Enhancement -->
    <div class="test-section">
        <h2>Test 2: Content Enhancement</h2>
        <div class="form-group">
            <label for="enhance-content">Content to Enhance:</label>
            <textarea id="enhance-content" rows="4" placeholder="Enter content that needs improvement...">My business provides technology solutions for small businesses in Ghana.</textarea>
        </div>
        
        <div class="form-group">
            <label for="enhance-action">Enhancement Action:</label>
            <select id="enhance-action">
                <option value="improve">Improve Writing</option>
                <option value="expand">Expand Content</option>
                <option value="localize">Localize for African Market</option>
                <option value="professional">Make More Professional</option>
            </select>
        </div>
        
        <button onclick="enhanceContent()" id="enhance-btn">Enhance Content</button>
        <div id="enhance-result" class="result" style="display: none;"></div>
    </div>
    
    <!-- Test 3: Real-time Suggestions -->
    <div class="test-section">
        <h2>Test 3: Real-time Suggestions</h2>
        <div class="form-group">
            <label for="realtime-content">Type here for real-time suggestions:</label>
            <textarea id="realtime-content" rows="4" placeholder="Start typing to see real-time AI suggestions..." oninput="getRealTimeSuggestions()"></textarea>
        </div>
        
        <div id="realtime-suggestions" class="ai-suggestions" style="display: none;"></div>
        <div id="realtime-result" class="result" style="display: none;"></div>
    </div>
    
    <!-- Test Results Summary -->
    <div class="test-section">
        <h2>📊 Test Results Summary</h2>
        <div id="test-summary">
            <p>Run the tests above to see results...</p>
        </div>
    </div>
</div>

<script>
// Configuration
const REST_BASE = 'http://localhost/swifmind-local/wordpress/wp-json/chatgabi/v1';
const NONCE = '<?php echo wp_create_nonce("wp_rest"); ?>'; // This would need to be generated by WordPress

// Test results tracking
let testResults = {
    suggestions: null,
    enhance: null,
    realtime: null
};

// Debounce function for real-time suggestions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Test 1: Get AI Suggestions
async function getSuggestions() {
    const btn = document.getElementById('suggestions-btn');
    const result = document.getElementById('suggestions-result');
    const display = document.getElementById('suggestions-display');
    
    btn.disabled = true;
    btn.textContent = 'Getting Suggestions...';
    result.style.display = 'block';
    result.className = 'result loading';
    result.textContent = 'Requesting AI suggestions...';
    
    const data = {
        field_type: document.getElementById('field-type').value,
        field_content: document.getElementById('field-content').value,
        user_context: {
            country: document.getElementById('user-country').value,
            industry: document.getElementById('user-industry').value
        }
    };
    
    try {
        const response = await fetch(`${REST_BASE}/ai-widgets/suggestions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': NONCE
            },
            body: JSON.stringify(data)
        });
        
        const responseData = await response.json();
        
        if (response.ok) {
            result.className = 'result success';
            result.textContent = 'AI suggestions received successfully!';
            testResults.suggestions = 'success';
            
            // Display suggestions
            if (responseData.data && responseData.data.suggestions) {
                display.style.display = 'block';
                display.innerHTML = '<h4>AI Suggestions:</h4>';
                responseData.data.suggestions.forEach(suggestion => {
                    const item = document.createElement('div');
                    item.className = 'suggestion-item';
                    item.textContent = suggestion.text || suggestion;
                    item.onclick = () => applySuggestion(suggestion.text || suggestion);
                    display.appendChild(item);
                });
            }
        } else {
            result.className = 'result error';
            result.textContent = `Error: ${responseData.message || 'Unknown error'}`;
            testResults.suggestions = 'error';
        }
    } catch (error) {
        result.className = 'result error';
        result.textContent = `Network error: ${error.message}`;
        testResults.suggestions = 'error';
    }
    
    btn.disabled = false;
    btn.textContent = 'Get AI Suggestions';
    updateTestSummary();
}

// Test 2: Enhance Content
async function enhanceContent() {
    const btn = document.getElementById('enhance-btn');
    const result = document.getElementById('enhance-result');
    
    btn.disabled = true;
    btn.textContent = 'Enhancing...';
    result.style.display = 'block';
    result.className = 'result loading';
    result.textContent = 'Enhancing content with AI...';
    
    const data = {
        field_id: 'test-field',
        action: document.getElementById('enhance-action').value,
        current_content: document.getElementById('enhance-content').value,
        user_context: {
            country: document.getElementById('user-country').value,
            industry: document.getElementById('user-industry').value
        }
    };
    
    try {
        const response = await fetch(`${REST_BASE}/ai-widgets/enhance`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': NONCE
            },
            body: JSON.stringify(data)
        });
        
        const responseData = await response.json();
        
        if (response.ok) {
            result.className = 'result success';
            result.innerHTML = `<strong>Enhanced Content:</strong><br>${responseData.data.enhanced_content || 'Content enhanced successfully!'}`;
            testResults.enhance = 'success';
        } else {
            result.className = 'result error';
            result.textContent = `Error: ${responseData.message || 'Unknown error'}`;
            testResults.enhance = 'error';
        }
    } catch (error) {
        result.className = 'result error';
        result.textContent = `Network error: ${error.message}`;
        testResults.enhance = 'error';
    }
    
    btn.disabled = false;
    btn.textContent = 'Enhance Content';
    updateTestSummary();
}

// Test 3: Real-time Suggestions (debounced)
const getRealTimeSuggestions = debounce(async function() {
    const content = document.getElementById('realtime-content').value;
    const result = document.getElementById('realtime-result');
    const display = document.getElementById('realtime-suggestions');
    
    if (content.length < 10) {
        display.style.display = 'none';
        return;
    }
    
    result.style.display = 'block';
    result.className = 'result loading';
    result.textContent = 'Getting real-time suggestions...';
    
    const data = {
        field_type: 'business_description',
        partial_content: content,
        user_context: {
            country: document.getElementById('user-country').value,
            industry: document.getElementById('user-industry').value
        }
    };
    
    try {
        const response = await fetch(`${REST_BASE}/ai-widgets/real-time-suggestions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': NONCE
            },
            body: JSON.stringify(data)
        });
        
        const responseData = await response.json();
        
        if (response.ok) {
            result.className = 'result success';
            result.textContent = 'Real-time suggestions received!';
            testResults.realtime = 'success';
            
            // Display real-time suggestions
            if (responseData.data && responseData.data.suggestions) {
                display.style.display = 'block';
                display.innerHTML = '<h4>Real-time Suggestions:</h4>';
                responseData.data.suggestions.forEach(suggestion => {
                    const item = document.createElement('div');
                    item.className = 'suggestion-item';
                    item.textContent = suggestion.text || suggestion;
                    display.appendChild(item);
                });
            }
        } else {
            result.className = 'result error';
            result.textContent = `Error: ${responseData.message || 'Unknown error'}`;
            testResults.realtime = 'error';
        }
    } catch (error) {
        result.className = 'result error';
        result.textContent = `Network error: ${error.message}`;
        testResults.realtime = 'error';
    }
    
    updateTestSummary();
}, 1000);

// Apply suggestion to field
function applySuggestion(text) {
    const fieldContent = document.getElementById('field-content');
    fieldContent.value = text;
}

// Update test summary
function updateTestSummary() {
    const summary = document.getElementById('test-summary');
    const total = Object.values(testResults).filter(r => r !== null).length;
    const successful = Object.values(testResults).filter(r => r === 'success').length;
    
    if (total === 0) {
        summary.innerHTML = '<p>Run the tests above to see results...</p>';
        return;
    }
    
    summary.innerHTML = `
        <h3>Test Results: ${successful}/${total} Successful</h3>
        <ul>
            <li>AI Suggestions: ${testResults.suggestions ? (testResults.suggestions === 'success' ? '✅ Success' : '❌ Failed') : '⏳ Not tested'}</li>
            <li>Content Enhancement: ${testResults.enhance ? (testResults.enhance === 'success' ? '✅ Success' : '❌ Failed') : '⏳ Not tested'}</li>
            <li>Real-time Suggestions: ${testResults.realtime ? (testResults.realtime === 'success' ? '✅ Success' : '❌ Failed') : '⏳ Not tested'}</li>
        </ul>
        ${total === 3 ? (successful === 3 ? '<p class="success">🎉 All tests passed! AI widgets are fully functional.</p>' : '<p class="error">⚠️ Some tests failed. Check the error messages above.</p>') : '<p>Continue testing to get complete results.</p>'}
    `;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateTestSummary();
});
</script>

</body>
</html>
