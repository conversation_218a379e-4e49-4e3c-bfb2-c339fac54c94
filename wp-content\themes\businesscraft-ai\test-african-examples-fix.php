<?php
/**
 * Test African Examples Manager - PHP Deprecation Warnings Fix
 * Verifies that null value warnings are eliminated
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Set error reporting to catch deprecation warnings
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type for proper display
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>African Examples Manager - Deprecation Warnings Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-section { background: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #007cba; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 African Examples Manager - Deprecation Warnings Fix Test</h1>";
echo "<p>Testing the fixes for PHP deprecation warnings in the African Market Customization system.</p>";

$test_results = array();
$warnings_caught = array();

// Custom error handler to catch deprecation warnings
function test_error_handler($errno, $errstr, $errfile, $errline) {
    global $warnings_caught;
    
    if ($errno === E_DEPRECATED || strpos($errstr, 'Passing null to parameter') !== false) {
        $warnings_caught[] = array(
            'message' => $errstr,
            'file' => basename($errfile),
            'line' => $errline
        );
    }
    
    return false; // Don't suppress the error
}

// Set custom error handler
set_error_handler('test_error_handler');

// Test 1: Load African Examples Manager
echo "<div class='test-section'>";
echo "<h2>🧪 Test 1: Loading African Examples Manager</h2>";

try {
    require_once get_template_directory() . '/inc/african-examples-manager.php';
    
    if (function_exists('chatgabi_get_african_examples_manager')) {
        echo "<div class='success'>✅ African Examples Manager loaded successfully</div>";
        $test_results['manager_load'] = true;
    } else {
        echo "<div class='error'>❌ African Examples Manager function not found</div>";
        $test_results['manager_load'] = false;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error loading manager: " . $e->getMessage() . "</div>";
    $test_results['manager_load'] = false;
}
echo "</div>";

// Test 2: Create Manager Instance
echo "<div class='test-section'>";
echo "<h2>🧪 Test 2: Creating Manager Instance</h2>";

try {
    $examples_manager = chatgabi_get_african_examples_manager();
    
    if ($examples_manager instanceof ChatGABI_African_Examples_Manager) {
        echo "<div class='success'>✅ Manager instance created successfully</div>";
        $test_results['instance_creation'] = true;
    } else {
        echo "<div class='error'>❌ Failed to create manager instance</div>";
        $test_results['instance_creation'] = false;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error creating instance: " . $e->getMessage() . "</div>";
    $test_results['instance_creation'] = false;
}
echo "</div>";

// Test 3: Test Sample Data Normalization
echo "<div class='test-section'>";
echo "<h2>🧪 Test 3: Sample Data Processing</h2>";

try {
    if (isset($examples_manager)) {
        // Use reflection to access private method for testing
        $reflection = new ReflectionClass($examples_manager);
        $get_sample_method = $reflection->getMethod('get_sample_examples');
        $get_sample_method->setAccessible(true);
        
        $sample_data = $get_sample_method->invoke($examples_manager);
        
        if (is_array($sample_data) && !empty($sample_data)) {
            echo "<div class='success'>✅ Sample data retrieved: " . count($sample_data) . " examples</div>";
            
            // Check if all required fields are present
            $required_fields = array(
                'company_name', 'country', 'industry', 'business_type', 'success_story',
                'metrics', 'year', 'revenue_range', 'employee_count', 'funding_stage',
                'key_achievements', 'challenges_overcome', 'lessons_learned', 
                'contact_info', 'website_url', 'logo_url', 'is_featured', 'is_verified', 'status'
            );
            
            $all_fields_present = true;
            $first_example = $sample_data[0];
            
            foreach ($required_fields as $field) {
                if (!array_key_exists($field, $first_example)) {
                    echo "<div class='warning'>⚠️ Missing field: {$field}</div>";
                    $all_fields_present = false;
                }
            }
            
            if ($all_fields_present) {
                echo "<div class='success'>✅ All required fields present in sample data</div>";
                $test_results['sample_data'] = true;
            } else {
                echo "<div class='error'>❌ Some required fields missing</div>";
                $test_results['sample_data'] = false;
            }
            
        } else {
            echo "<div class='error'>❌ No sample data retrieved</div>";
            $test_results['sample_data'] = false;
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error processing sample data: " . $e->getMessage() . "</div>";
    $test_results['sample_data'] = false;
}
echo "</div>";

// Test 4: Test Add Example with Null Values
echo "<div class='test-section'>";
echo "<h2>🧪 Test 4: Testing Null Value Handling</h2>";

try {
    if (isset($examples_manager)) {
        // Test data with intentionally missing/null fields
        $test_data = array(
            'company_name' => 'Test Company',
            'country' => 'GH',
            'industry' => 'Technology',
            'success_story' => 'Test success story',
            'year' => 2023,
            // Intentionally omitting other fields to test null handling
        );
        
        // Clear previous warnings
        $warnings_caught = array();
        
        // This should not generate deprecation warnings
        $result = $examples_manager->add_example($test_data);
        
        if (empty($warnings_caught)) {
            echo "<div class='success'>✅ No deprecation warnings generated during add_example()</div>";
            $test_results['null_handling'] = true;
        } else {
            echo "<div class='error'>❌ Deprecation warnings still occurring:</div>";
            foreach ($warnings_caught as $warning) {
                echo "<div class='warning'>⚠️ {$warning['message']} in {$warning['file']}:{$warning['line']}</div>";
            }
            $test_results['null_handling'] = false;
        }
        
        if (!is_wp_error($result)) {
            echo "<div class='info'>💡 Test example added successfully (ID: {$result})</div>";
            
            // Clean up test data
            $examples_manager->delete_example($result);
            echo "<div class='info'>💡 Test example cleaned up</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error testing null handling: " . $e->getMessage() . "</div>";
    $test_results['null_handling'] = false;
}
echo "</div>";

// Test 5: Test Safe Sanitization Methods
echo "<div class='test-section'>";
echo "<h2>🧪 Test 5: Testing Safe Sanitization Methods</h2>";

try {
    if (isset($examples_manager)) {
        // Use reflection to test private sanitization methods
        $reflection = new ReflectionClass($examples_manager);
        
        $methods_to_test = array(
            'safe_sanitize_text_field',
            'safe_sanitize_textarea_field',
            'safe_wp_kses_post',
            'safe_esc_url_raw'
        );
        
        $sanitization_tests_passed = 0;
        
        foreach ($methods_to_test as $method_name) {
            try {
                $method = $reflection->getMethod($method_name);
                $method->setAccessible(true);
                
                // Clear previous warnings
                $warnings_caught = array();
                
                // Test with null value
                $result = $method->invoke($examples_manager, null);
                
                if (empty($warnings_caught) && $result === '') {
                    echo "<div class='success'>✅ {$method_name}() handles null values correctly</div>";
                    $sanitization_tests_passed++;
                } else {
                    echo "<div class='error'>❌ {$method_name}() still generates warnings or incorrect output</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error testing {$method_name}: " . $e->getMessage() . "</div>";
            }
        }
        
        if ($sanitization_tests_passed === count($methods_to_test)) {
            echo "<div class='success'>✅ All sanitization methods handle null values correctly</div>";
            $test_results['sanitization'] = true;
        } else {
            echo "<div class='error'>❌ Some sanitization methods still have issues</div>";
            $test_results['sanitization'] = false;
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error testing sanitization methods: " . $e->getMessage() . "</div>";
    $test_results['sanitization'] = false;
}
echo "</div>";

// Restore default error handler
restore_error_handler();

// Summary
echo "<div class='test-section'>";
echo "<h2>📊 Test Summary</h2>";

$total_tests = count($test_results);
$passed_tests = array_sum($test_results);
$success_rate = ($passed_tests / $total_tests) * 100;

echo "<div class='info'>";
echo "<strong>Test Results:</strong><br>";
echo "• Total Tests: {$total_tests}<br>";
echo "• Passed: {$passed_tests}<br>";
echo "• Failed: " . ($total_tests - $passed_tests) . "<br>";
echo "• Success Rate: " . round($success_rate, 1) . "%<br>";
echo "</div>";

if ($success_rate === 100) {
    echo "<div class='success'>";
    echo "<h3>🎉 All Tests Passed!</h3>";
    echo "<p>The PHP deprecation warnings have been successfully fixed. The African Examples Manager now properly handles null values and prevents warnings during initialization.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>⚠️ Some Tests Failed</h3>";
    echo "<p>There are still some issues that need to be addressed. Please review the failed tests above.</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='initialize-african-market-customization.php' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; text-decoration: none;'>Run Full Initialization</a>";
echo "</div>";

echo "</div>";

echo "</div></body></html>";
?>
