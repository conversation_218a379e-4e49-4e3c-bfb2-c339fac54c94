<?php
/**
 * Multi-Currency Payment System for African Markets
 * Enhanced Paystack integration with local currency support
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Multi-Currency Payment Manager Class
 */
class ChatGABI_Multi_Currency_Payment {

    private $supported_currencies;
    private $tax_rates;
    
    public function __construct() {
        $this->init_supported_currencies();
        $this->init_tax_rates();
    }

    /**
     * Initialize supported currencies with enhanced data
     */
    private function init_supported_currencies() {
        $this->supported_currencies = array(
            'GH' => array(
                'currency' => 'GHS',
                'symbol' => '₵',
                'name' => 'Ghanaian Cedi',
                'decimal_places' => 2,
                'paystack_supported' => true,
                'payment_methods' => array('card', 'mobile_money', 'bank_transfer'),
                'mobile_money_providers' => array('mtn', 'vodafone', 'airteltigo'),
                'popular_banks' => array('GCB Bank', 'Ecobank', 'Fidelity Bank'),
                'tax_name' => 'VAT',
                'tax_rate' => 15.0,
                'business_registration' => 'Registrar General Department'
            ),
            'KE' => array(
                'currency' => 'KES',
                'symbol' => 'KSh',
                'name' => 'Kenyan Shilling',
                'decimal_places' => 2,
                'paystack_supported' => true,
                'payment_methods' => array('card', 'mobile_money', 'bank_transfer'),
                'mobile_money_providers' => array('mpesa', 'airtel_money'),
                'popular_banks' => array('Equity Bank', 'KCB', 'Co-operative Bank'),
                'tax_name' => 'VAT',
                'tax_rate' => 16.0,
                'business_registration' => 'eCitizen Portal'
            ),
            'NG' => array(
                'currency' => 'NGN',
                'symbol' => '₦',
                'name' => 'Nigerian Naira',
                'decimal_places' => 2,
                'paystack_supported' => true,
                'payment_methods' => array('card', 'bank_transfer', 'ussd', 'qr'),
                'mobile_money_providers' => array('opay', 'palmpay', 'kuda'),
                'popular_banks' => array('GTBank', 'Access Bank', 'Zenith Bank'),
                'tax_name' => 'VAT',
                'tax_rate' => 7.5,
                'business_registration' => 'Corporate Affairs Commission (CAC)'
            ),
            'ZA' => array(
                'currency' => 'ZAR',
                'symbol' => 'R',
                'name' => 'South African Rand',
                'decimal_places' => 2,
                'paystack_supported' => true,
                'payment_methods' => array('card', 'eft', 'instant_eft'),
                'mobile_money_providers' => array('snapscan', 'zapper', 'yoco'),
                'popular_banks' => array('FNB', 'Standard Bank', 'Absa', 'Nedbank'),
                'tax_name' => 'VAT',
                'tax_rate' => 15.0,
                'business_registration' => 'Companies and Intellectual Property Commission (CIPC)'
            )
        );
    }

    /**
     * Initialize tax rates and regulations
     */
    private function init_tax_rates() {
        $this->tax_rates = array(
            'GH' => array(
                'vat_rate' => 15.0,
                'vat_threshold' => 200000, // GHS
                'corporate_tax' => 25.0,
                'withholding_tax' => 5.0
            ),
            'KE' => array(
                'vat_rate' => 16.0,
                'vat_threshold' => 5000000, // KES
                'corporate_tax' => 30.0,
                'withholding_tax' => 5.0
            ),
            'NG' => array(
                'vat_rate' => 7.5,
                'vat_threshold' => ********, // NGN
                'corporate_tax' => 30.0,
                'withholding_tax' => 10.0
            ),
            'ZA' => array(
                'vat_rate' => 15.0,
                'vat_threshold' => 1000000, // ZAR
                'corporate_tax' => 28.0,
                'withholding_tax' => 20.0
            )
        );
    }

    /**
     * Get currency information for a country
     */
    public function get_currency_info($country_code) {
        return isset($this->supported_currencies[$country_code]) 
            ? $this->supported_currencies[$country_code] 
            : $this->supported_currencies['GH']; // Default to Ghana
    }

    /**
     * Get enhanced exchange rates with caching
     */
    public function get_exchange_rates() {
        $cache_key = 'chatgabi_enhanced_exchange_rates';
        $cached_rates = get_transient($cache_key);
        
        if ($cached_rates) {
            return $cached_rates;
        }

        // Try multiple exchange rate APIs for reliability
        $rates = $this->fetch_exchange_rates_multi_source();
        
        if (!$rates) {
            // Fallback to default rates
            $rates = array(
                'GHS' => 12.50,
                'KES' => 135.00,
                'NGN' => 780.00,
                'ZAR' => 18.50,
                'USD' => 1.00
            );
        }

        // Cache for 4 hours
        set_transient($cache_key, $rates, 4 * HOUR_IN_SECONDS);
        
        return $rates;
    }

    /**
     * Fetch exchange rates from multiple sources
     */
    private function fetch_exchange_rates_multi_source() {
        $sources = array(
            'https://api.exchangerate-api.com/v4/latest/USD',
            'https://api.fixer.io/latest?base=USD&access_key=' . get_option('chatgabi_fixer_api_key', ''),
            'https://openexchangerates.org/api/latest.json?app_id=' . get_option('chatgabi_oxr_api_key', '')
        );

        foreach ($sources as $source) {
            $rates = $this->fetch_rates_from_source($source);
            if ($rates) {
                return $rates;
            }
        }

        return false;
    }

    /**
     * Fetch rates from a specific source
     */
    private function fetch_rates_from_source($url) {
        if (empty($url) || strpos($url, 'access_key=&') !== false || strpos($url, 'app_id=&') !== false) {
            return false; // Skip if API key is missing
        }

        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => 'ChatGABI Multi-Currency System'
        ));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return false;
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);
        
        if (!isset($data['rates'])) {
            return false;
        }

        // Extract only needed currencies
        $needed_currencies = array('GHS', 'KES', 'NGN', 'ZAR', 'USD');
        $rates = array();

        foreach ($needed_currencies as $currency) {
            if (isset($data['rates'][$currency])) {
                $rates[$currency] = $data['rates'][$currency];
            }
        }

        $rates['USD'] = 1.00; // Ensure USD is always 1.00
        
        return count($rates) >= 4 ? $rates : false;
    }

    /**
     * Convert amount with enhanced precision
     */
    public function convert_currency($amount, $from_currency, $to_currency) {
        if ($from_currency === $to_currency) {
            return $amount;
        }

        $rates = $this->get_exchange_rates();
        
        if (!isset($rates[$from_currency]) || !isset($rates[$to_currency])) {
            return $amount; // Return original if conversion not possible
        }

        // Convert to USD first, then to target currency
        $usd_amount = $amount / $rates[$from_currency];
        $converted_amount = $usd_amount * $rates[$to_currency];
        
        return round($converted_amount, 2);
    }

    /**
     * Get localized pricing with tax calculations
     */
    public function get_localized_pricing($country_code) {
        $currency_info = $this->get_currency_info($country_code);
        $tax_info = $this->tax_rates[$country_code] ?? $this->tax_rates['GH'];
        
        // Base USD prices
        $base_packages = array(
            'starter' => array('credits' => 500, 'price_usd' => 5.00),
            'growth' => array('credits' => 1500, 'price_usd' => 15.00),
            'business' => array('credits' => 3000, 'price_usd' => 30.00),
            'enterprise' => array('credits' => 5000, 'price_usd' => 50.00),
        );

        $localized_packages = array();
        
        foreach ($base_packages as $package_key => $package_data) {
            $local_price = $this->convert_currency(
                $package_data['price_usd'], 
                'USD', 
                $currency_info['currency']
            );
            
            // Calculate tax if applicable
            $tax_amount = 0;
            $price_with_tax = $local_price;
            
            if ($this->should_apply_tax($country_code, $local_price)) {
                $tax_amount = $local_price * ($tax_info['vat_rate'] / 100);
                $price_with_tax = $local_price + $tax_amount;
            }
            
            $localized_packages[$package_key] = array(
                'credits' => $package_data['credits'],
                'price_usd' => $package_data['price_usd'],
                'price_local' => $local_price,
                'price_with_tax' => $price_with_tax,
                'tax_amount' => $tax_amount,
                'tax_rate' => $tax_info['vat_rate'],
                'tax_name' => $currency_info['tax_name'],
                'currency' => $currency_info['currency'],
                'currency_symbol' => $currency_info['symbol'],
                'formatted_price' => $this->format_price($price_with_tax, $currency_info),
                'payment_methods' => $currency_info['payment_methods'],
                'mobile_money_providers' => $currency_info['mobile_money_providers'] ?? array()
            );
        }
        
        return $localized_packages;
    }

    /**
     * Check if tax should be applied
     */
    private function should_apply_tax($country_code, $amount) {
        $tax_info = $this->tax_rates[$country_code] ?? $this->tax_rates['GH'];
        
        // For digital services, most African countries apply VAT
        // This is a simplified check - in production, you'd want more sophisticated logic
        return true;
    }

    /**
     * Format price according to local conventions
     */
    public function format_price($amount, $currency_info) {
        $symbol = $currency_info['symbol'];
        $decimal_places = $currency_info['decimal_places'];
        
        // Different formatting for different countries
        switch ($currency_info['currency']) {
            case 'GHS':
                return $symbol . number_format($amount, $decimal_places);
            case 'KES':
                return $symbol . ' ' . number_format($amount, $decimal_places);
            case 'NGN':
                return $symbol . number_format($amount, $decimal_places);
            case 'ZAR':
                return $symbol . number_format($amount, $decimal_places);
            default:
                return $symbol . number_format($amount, $decimal_places);
        }
    }

    /**
     * Enhanced Paystack payment initialization
     */
    public function initiate_enhanced_payment($email, $package, $user_id, $country_code) {
        $pricing = $this->get_localized_pricing($country_code);
        
        if (!isset($pricing[$package])) {
            return new WP_Error('invalid_package', 'Invalid package selected');
        }
        
        $package_data = $pricing[$package];
        $currency_info = $this->get_currency_info($country_code);
        
        // Generate unique reference
        $reference = 'chatgabi_' . $country_code . '_' . $user_id . '_' . time() . '_' . wp_rand(1000, 9999);
        
        // Prepare payment data
        $payment_data = array(
            'email' => $email,
            'amount' => $package_data['price_with_tax'] * 100, // Convert to subunits
            'currency' => $currency_info['currency'],
            'reference' => $reference,
            'callback_url' => home_url('/payment-success/'),
            'channels' => $currency_info['payment_methods'],
            'metadata' => array(
                'user_id' => $user_id,
                'package' => $package,
                'country' => $country_code,
                'credits' => $package_data['credits'],
                'base_amount' => $package_data['price_local'],
                'tax_amount' => $package_data['tax_amount'],
                'tax_rate' => $package_data['tax_rate'],
                'usd_amount' => $package_data['price_usd'],
                'exchange_rate' => $this->get_exchange_rates()[$currency_info['currency']] ?? 1,
                'payment_timestamp' => current_time('mysql')
            )
        );
        
        // Call existing Paystack integration
        return $this->call_paystack_api($payment_data);
    }

    /**
     * Call Paystack API with enhanced error handling
     */
    private function call_paystack_api($payment_data) {
        $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') 
            ? BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY 
            : get_option('businesscraft_ai_paystack_secret_key');

        if (empty($secret_key)) {
            return new WP_Error('missing_api_key', 'Paystack API key not configured');
        }

        $response = wp_remote_post('https://api.paystack.co/transaction/initialize', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $secret_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($payment_data),
            'timeout' => 30,
        ));

        if (is_wp_error($response)) {
            return new WP_Error('api_error', 'Failed to connect to Paystack: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body, true);

        if ($response_code !== 200 || !$response_data['status']) {
            $error_message = isset($response_data['message']) 
                ? $response_data['message'] 
                : 'Payment initialization failed';
            
            return new WP_Error('payment_failed', $error_message);
        }

        // Log successful payment initiation
        $this->log_payment_initiation($payment_data);

        return array(
            'status' => 'success',
            'data' => $response_data['data'],
            'reference' => $payment_data['reference']
        );
    }

    /**
     * Log payment initiation for tracking
     */
    private function log_payment_initiation($payment_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_payment_logs';
        
        // Create table if it doesn't exist
        $this->create_payment_logs_table();
        
        $wpdb->insert(
            $table_name,
            array(
                'user_id' => $payment_data['metadata']['user_id'],
                'reference' => $payment_data['reference'],
                'amount' => $payment_data['amount'] / 100,
                'currency' => $payment_data['currency'],
                'country' => $payment_data['metadata']['country'],
                'package' => $payment_data['metadata']['package'],
                'status' => 'initiated',
                'payment_data' => json_encode($payment_data),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s')
        );
    }

    /**
     * Create payment logs table
     */
    private function create_payment_logs_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_payment_logs';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            reference varchar(100) NOT NULL,
            amount decimal(10,2) NOT NULL,
            currency varchar(3) NOT NULL,
            country varchar(2) NOT NULL,
            package varchar(50) NOT NULL,
            status varchar(20) NOT NULL DEFAULT 'initiated',
            payment_data longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY reference (reference),
            KEY user_id (user_id),
            KEY country (country),
            KEY status (status),
            KEY created_at (created_at)
        ) {$charset_collate};";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

/**
 * Get Multi-Currency Payment Manager instance
 */
function chatgabi_get_multi_currency_payment() {
    static $instance = null;
    
    if ($instance === null) {
        $instance = new ChatGABI_Multi_Currency_Payment();
    }
    
    return $instance;
}
