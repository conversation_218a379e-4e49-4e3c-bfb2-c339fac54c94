<?php
/**
 * Initialize African Market Customization for ChatGABI
 * Sets up database tables, sample data, and configuration
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Allow direct access for initialization script
    require_once('../../../wp-config.php');
}

// Set content type for proper display
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI African Market Customization Initialization</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #007cba; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: center; flex: 1; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
        .progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 10px 0; }
        .progress-bar { background: #007cba; height: 100%; border-radius: 4px; transition: width 0.3s; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #005a87; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🌍 ChatGABI African Market Customization</h1>";
echo "<p>Initializing comprehensive African market features including business examples, cultural themes, and multi-currency payments.</p>";

$initialization_steps = 0;
$completed_steps = 0;
$errors = array();

// Step 1: Load required files
echo "<div class='step'>";
echo "<h2>📁 Step 1: Loading Required Files</h2>";
$initialization_steps++;

$required_files = array(
    'inc/african-examples-manager.php' => 'African Examples Manager',
    'inc/admin-african-examples.php' => 'Admin Interface',
    'inc/african-theme-customizer.php' => 'Theme Customizer',
    'inc/multi-currency-payment.php' => 'Multi-Currency Payment System',
    'inc/african-context-engine.php' => 'African Context Engine'
);

$files_loaded = 0;
foreach ($required_files as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        require_once $file_path;
        echo "<div class='success'>✅ {$description} loaded</div>";
        $files_loaded++;
    } else {
        echo "<div class='error'>❌ {$description} not found: {$file}</div>";
        $errors[] = "Missing file: {$file}";
    }
}

if ($files_loaded === count($required_files)) {
    echo "<div class='success'>🎉 All required files loaded successfully!</div>";
    $completed_steps++;
} else {
    echo "<div class='error'>⚠️ Some required files are missing. Please ensure all files are uploaded.</div>";
}
echo "</div>";

// Step 2: Create Database Tables
echo "<div class='step'>";
echo "<h2>🗄️ Step 2: Creating Database Tables</h2>";
$initialization_steps++;

try {
    // Create African examples table
    if (function_exists('chatgabi_get_african_examples_manager')) {
        $examples_manager = chatgabi_get_african_examples_manager();
        $table_created = $examples_manager->create_table();
        
        if ($table_created) {
            echo "<div class='success'>✅ African business examples table created</div>";
        } else {
            echo "<div class='warning'>⚠️ African business examples table may already exist</div>";
        }
    } else {
        echo "<div class='error'>❌ African Examples Manager not available</div>";
        $errors[] = "African Examples Manager not loaded";
    }
    
    // Create multi-currency payment logs table
    if (function_exists('chatgabi_get_multi_currency_payment')) {
        $payment_manager = chatgabi_get_multi_currency_payment();
        // Table creation is handled automatically in the payment manager
        echo "<div class='success'>✅ Multi-currency payment system initialized</div>";
    } else {
        echo "<div class='error'>❌ Multi-Currency Payment Manager not available</div>";
        $errors[] = "Multi-Currency Payment Manager not loaded";
    }
    
    $completed_steps++;
    echo "<div class='success'>🎉 Database tables created successfully!</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database creation error: " . $e->getMessage() . "</div>";
    $errors[] = "Database error: " . $e->getMessage();
}
echo "</div>";

// Step 3: Initialize Sample Business Examples
echo "<div class='step'>";
echo "<h2>🏢 Step 3: Initializing Sample Business Examples</h2>";
$initialization_steps++;

try {
    if (function_exists('chatgabi_get_african_examples_manager')) {
        $examples_manager = chatgabi_get_african_examples_manager();
        $sample_data_created = $examples_manager->initialize_sample_data();
        
        if ($sample_data_created) {
            echo "<div class='success'>✅ Sample business examples added successfully</div>";
            
            // Get statistics
            $stats = $examples_manager->get_statistics();
            echo "<div class='stats'>";
            echo "<div class='stat-card'><div class='stat-number'>{$stats['total']}</div><div>Total Examples</div></div>";
            echo "<div class='stat-card'><div class='stat-number'>{$stats['featured']}</div><div>Featured</div></div>";
            echo "<div class='stat-card'><div class='stat-number'>{$stats['verified']}</div><div>Verified</div></div>";
            echo "</div>";
            
            // Show examples by country
            if (!empty($stats['by_country'])) {
                echo "<div class='info'><strong>Examples by Country:</strong><br>";
                foreach ($stats['by_country'] as $country_stat) {
                    $country_name = chatgabi_get_country_name($country_stat->country);
                    echo "• {$country_name}: {$country_stat->count} examples<br>";
                }
                echo "</div>";
            }
            
        } else {
            echo "<div class='warning'>⚠️ Sample data already exists or could not be created</div>";
        }
        
        $completed_steps++;
    } else {
        echo "<div class='error'>❌ Cannot initialize sample data - Examples Manager not available</div>";
        $errors[] = "Examples Manager not available for sample data";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Sample data initialization error: " . $e->getMessage() . "</div>";
    $errors[] = "Sample data error: " . $e->getMessage();
}
echo "</div>";

// Step 4: Configure Theme Customizer Defaults
echo "<div class='step'>";
echo "<h2>🎨 Step 4: Configuring Theme Customizer</h2>";
$initialization_steps++;

try {
    // Set default customizer values
    $customizer_defaults = array(
        'chatgabi_color_scheme' => 'default',
        'chatgabi_cultural_patterns' => false,
        'chatgabi_african_typography' => false,
        'chatgabi_african_imagery' => true,
        'chatgabi_primary_region' => 'multi',
        'chatgabi_cultural_sensitivity' => true,
        'chatgabi_ubuntu_philosophy' => true
    );
    
    $defaults_set = 0;
    foreach ($customizer_defaults as $setting => $default_value) {
        $current_value = get_theme_mod($setting);
        if ($current_value === false || $current_value === '') {
            set_theme_mod($setting, $default_value);
            $defaults_set++;
        }
    }
    
    echo "<div class='success'>✅ Theme customizer configured with {$defaults_set} default settings</div>";
    echo "<div class='info'>💡 You can customize these settings in <strong>Appearance → Customize → African Market Customization</strong></div>";
    
    $completed_steps++;
} catch (Exception $e) {
    echo "<div class='error'>❌ Theme customizer configuration error: " . $e->getMessage() . "</div>";
    $errors[] = "Customizer error: " . $e->getMessage();
}
echo "</div>";

// Step 5: Test Multi-Currency System
echo "<div class='step'>";
echo "<h2>💰 Step 5: Testing Multi-Currency System</h2>";
$initialization_steps++;

try {
    if (function_exists('chatgabi_get_multi_currency_payment')) {
        $payment_manager = chatgabi_get_multi_currency_payment();
        
        // Test currency conversion
        $test_countries = array('GH', 'KE', 'NG', 'ZA');
        echo "<div class='info'><strong>Currency Support Test:</strong><br>";
        
        foreach ($test_countries as $country) {
            $currency_info = $payment_manager->get_currency_info($country);
            $pricing = $payment_manager->get_localized_pricing($country);
            
            echo "• {$country}: {$currency_info['name']} ({$currency_info['symbol']}) - ";
            echo "Starter Package: {$pricing['starter']['formatted_price']}<br>";
        }
        echo "</div>";
        
        echo "<div class='success'>✅ Multi-currency system is working correctly</div>";
        $completed_steps++;
    } else {
        echo "<div class='error'>❌ Multi-currency system not available</div>";
        $errors[] = "Multi-currency system not loaded";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Multi-currency test error: " . $e->getMessage() . "</div>";
    $errors[] = "Multi-currency error: " . $e->getMessage();
}
echo "</div>";

// Progress Summary
$progress_percentage = ($completed_steps / $initialization_steps) * 100;
echo "<div class='step'>";
echo "<h2>📊 Initialization Summary</h2>";
echo "<div class='progress'>";
echo "<div class='progress-bar' style='width: {$progress_percentage}%'></div>";
echo "</div>";
echo "<p><strong>Progress:</strong> {$completed_steps}/{$initialization_steps} steps completed ({$progress_percentage}%)</p>";

if (empty($errors)) {
    echo "<div class='success'>";
    echo "<h3>🎉 African Market Customization Initialized Successfully!</h3>";
    echo "<p>All components have been set up and are ready to use:</p>";
    echo "<ul>";
    echo "<li>✅ Business examples database with sample data</li>";
    echo "<li>✅ African theme customization options</li>";
    echo "<li>✅ Multi-currency payment system</li>";
    echo "<li>✅ Enhanced African Context Engine</li>";
    echo "<li>✅ Admin interface for managing examples</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🚀 Next Steps:</h3>";
    echo "<ul>";
    echo "<li>Visit <strong>ChatGABI → Business Examples</strong> to manage African business case studies</li>";
    echo "<li>Go to <strong>Appearance → Customize → African Market Customization</strong> to configure visual themes</li>";
    echo "<li>Test the multi-currency payment system with different country selections</li>";
    echo "<li>Review the enhanced AI responses with African context and real business examples</li>";
    echo "</ul>";
    echo "</div>";
    
} else {
    echo "<div class='error'>";
    echo "<h3>⚠️ Initialization Completed with Issues</h3>";
    echo "<p>The following errors occurred:</p>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='" . admin_url('admin.php?page=chatgabi') . "' class='btn'>Go to ChatGABI Dashboard</a> ";
echo "<a href='" . admin_url('admin.php?page=chatgabi-african-examples') . "' class='btn btn-secondary'>Manage Business Examples</a> ";
echo "<a href='" . admin_url('customize.php') . "' class='btn btn-secondary'>Customize Theme</a>";
echo "</div>";

echo "</div>";

echo "</div></body></html>";
?>
