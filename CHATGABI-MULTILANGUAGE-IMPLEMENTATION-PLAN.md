# ChatGABI Multi-Language Implementation Plan

## 🎯 **Implementation Overview**

This document provides a detailed, step-by-step implementation plan for establishing comprehensive multi-language support in ChatGABI, focusing on WordPress internationalization standards and African market requirements.

## 📋 **Phase 1: Critical Infrastructure Setup (Week 1-2)**

### **Task 1.1: Text Domain Standardization**

#### **Objective**: Standardize all text domains to `'chatgabi'`

#### **Files to Update:**
1. `wp-content/themes/businesscraft-ai/functions.php` (30+ instances)
2. `wp-content/themes/businesscraft-ai/inc/*.php` (95+ instances)
3. `wp-content/themes/businesscraft-ai/page-*.php` (40+ instances)
4. `wp-content/themes/businesscraft-ai/template-parts/*.php` (20+ instances)

#### **Implementation Steps:**
```bash
# 1. Search and replace inconsistent text domains
find wp-content/themes/businesscraft-ai -name "*.php" -exec sed -i 's/businesscraft-ai/chatgabi/g' {} \;

# 2. Verify changes
grep -r "businesscraft-ai" wp-content/themes/businesscraft-ai --include="*.php"
```

#### **Code Changes Required:**
```php
// Before:
__('Loading...', 'businesscraft-ai')
_e('Success', 'businesscraft-ai')

// After:
__('Loading...', 'chatgabi')
_e('Success', 'chatgabi')
```

### **Task 1.2: Text Domain Loading Implementation**

#### **Objective**: Add proper text domain loading to theme

#### **File**: `wp-content/themes/businesscraft-ai/functions.php`

#### **Implementation:**
```php
/**
 * Load theme text domain for internationalization
 */
function chatgabi_load_textdomain() {
    load_theme_textdomain('chatgabi', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'chatgabi_load_textdomain', 1);
```

### **Task 1.3: Language Directory Structure**

#### **Objective**: Create proper language files structure

#### **Directory Creation:**
```bash
mkdir -p wp-content/themes/businesscraft-ai/languages
```

#### **Required Files:**
```
wp-content/themes/businesscraft-ai/languages/
├── chatgabi.pot                    # Template file
├── chatgabi-en_US.po               # English (US)
├── chatgabi-en_US.mo
├── chatgabi-sw_KE.po               # Swahili (Kenya)
├── chatgabi-sw_KE.mo
├── chatgabi-yo_NG.po               # Yoruba (Nigeria)
├── chatgabi-yo_NG.mo
├── chatgabi-tw_GH.po               # Twi (Ghana)
├── chatgabi-tw_GH.mo
└── chatgabi-zu_ZA.po               # Zulu (South Africa)
└── chatgabi-zu_ZA.mo
```

### **Task 1.4: POT File Generation**

#### **Objective**: Generate translation template file

#### **Tools Required:**
- WP-CLI with i18n command
- Poedit (for manual editing)

#### **Generation Command:**
```bash
wp i18n make-pot wp-content/themes/businesscraft-ai wp-content/themes/businesscraft-ai/languages/chatgabi.pot --domain=chatgabi
```

#### **Manual Verification:**
- Check all translatable strings are included
- Verify context comments are present
- Ensure proper text domain usage

## 📋 **Phase 2: Core Localization (Week 3-4)**

### **Task 2.1: JavaScript Internationalization**

#### **Objective**: Implement wp.i18n for frontend scripts

#### **Files to Update:**
1. `assets/js/chat-block.js`
2. `assets/js/templates-interface.js`
3. `assets/js/user-preferences.js`
4. `assets/js/onboarding.js`

#### **Implementation Example:**
```javascript
// Before:
const loadingText = businesscraftAI.strings.loading;

// After:
import { __ } from '@wordpress/i18n';
const loadingText = __('Loading...', 'chatgabi');
```

#### **Script Enqueue Updates:**
```php
// Add to functions.php
wp_set_script_translations('chatgabi-chat-block', 'chatgabi', get_template_directory() . '/languages');
```

### **Task 2.2: Currency Formatting System**

#### **Objective**: Implement localized currency formatting

#### **New File**: `inc/currency-formatter.php`

#### **Implementation:**
```php
<?php
/**
 * Currency formatting for African markets
 */
function chatgabi_get_currency_data($country_code) {
    $currencies = array(
        'GH' => array(
            'symbol' => 'GH₵',
            'code' => 'GHS',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.'
        ),
        'KE' => array(
            'symbol' => 'KSh',
            'code' => 'KES',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.'
        ),
        'NG' => array(
            'symbol' => '₦',
            'code' => 'NGN',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.'
        ),
        'ZA' => array(
            'symbol' => 'R',
            'code' => 'ZAR',
            'position' => 'before',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.'
        )
    );
    
    return $currencies[$country_code] ?? $currencies['GH'];
}

function chatgabi_format_currency($amount, $country_code) {
    $currency = chatgabi_get_currency_data($country_code);
    $formatted_amount = number_format(
        $amount,
        $currency['decimal_places'],
        $currency['decimal_separator'],
        $currency['thousands_separator']
    );
    
    return $currency['position'] === 'before' 
        ? $currency['symbol'] . $formatted_amount
        : $formatted_amount . $currency['symbol'];
}
```

### **Task 2.3: Language Switching Optimization**

#### **Objective**: Improve language switching performance

#### **New File**: `inc/language-switcher.php`

#### **Implementation:**
```php
<?php
/**
 * Enhanced language switching functionality
 */
function chatgabi_render_language_switcher($current_language = null) {
    if (!$current_language) {
        $current_language = chatgabi_get_user_preferred_language();
    }
    
    $supported_languages = chatgabi_get_supported_languages();
    
    ob_start();
    ?>
    <div class="chatgabi-language-switcher">
        <select id="chatgabi-language-select" data-current="<?php echo esc_attr($current_language); ?>">
            <?php foreach ($supported_languages as $code => $language): ?>
                <option value="<?php echo esc_attr($code); ?>" 
                        <?php selected($current_language, $code); ?>>
                    <?php echo esc_html($language['flag'] . ' ' . $language['native_name']); ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <script>
    document.getElementById('chatgabi-language-select').addEventListener('change', function() {
        const newLanguage = this.value;
        chatgabi_switch_language(newLanguage);
    });
    
    function chatgabi_switch_language(language) {
        // AJAX call to save preference
        fetch(chatgabiAjax.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'chatgabi_switch_language',
                language: language,
                nonce: chatgabiAjax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page with new language
                window.location.reload();
            }
        });
    }
    </script>
    <?php
    return ob_get_clean();
}

// AJAX handler for language switching
add_action('wp_ajax_chatgabi_switch_language', 'chatgabi_ajax_switch_language');
add_action('wp_ajax_nopriv_chatgabi_switch_language', 'chatgabi_ajax_switch_language');

function chatgabi_ajax_switch_language() {
    check_ajax_referer('chatgabi_language_nonce', 'nonce');
    
    $language = sanitize_text_field($_POST['language']);
    $supported_languages = array_keys(chatgabi_get_supported_languages());
    
    if (!in_array($language, $supported_languages)) {
        wp_send_json_error('Invalid language');
    }
    
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        update_user_meta($user_id, 'chatgabi_preferred_language', $language);
    } else {
        // Store in session for non-logged-in users
        if (!session_id()) {
            session_start();
        }
        $_SESSION['chatgabi_language'] = $language;
    }
    
    wp_send_json_success('Language switched successfully');
}
```

## 📋 **Phase 3: African Market Enhancement (Week 5-6)**

### **Task 3.1: Template Content Translation**

#### **Objective**: Translate business templates to African languages

#### **Database Schema Enhancement:**
```sql
-- Add multi-language content support
ALTER TABLE wp_chatgabi_prompt_templates 
ADD COLUMN translated_content JSON COMMENT 'Multi-language template content';

-- Update existing templates with translation structure
UPDATE wp_chatgabi_prompt_templates 
SET translated_content = JSON_OBJECT('en', prompt_content)
WHERE translated_content IS NULL;
```

#### **Translation Function:**
```php
function chatgabi_get_template_content($template_id, $language = 'en') {
    global $wpdb;
    
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT translated_content, prompt_content FROM {$wpdb->prefix}chatgabi_prompt_templates WHERE id = %d",
        $template_id
    ));
    
    if (!$template) {
        return false;
    }
    
    $translated_content = json_decode($template->translated_content, true);
    
    // Return translated version if available
    if (isset($translated_content[$language])) {
        return $translated_content[$language];
    }
    
    // Fallback to English
    if (isset($translated_content['en'])) {
        return $translated_content['en'];
    }
    
    // Final fallback to original content
    return $template->prompt_content;
}
```

### **Task 3.2: Cultural Context Integration**

#### **Objective**: Add local business terminology and cultural context

#### **New File**: `inc/cultural-context.php`

#### **Implementation:**
```php
<?php
/**
 * Cultural context and local business terminology
 */
function chatgabi_get_cultural_business_terms($language, $country = null) {
    $terms = array(
        'en' => array(
            'entrepreneur' => 'entrepreneur',
            'startup' => 'startup',
            'business_plan' => 'business plan',
            'market_research' => 'market research',
            'funding' => 'funding'
        ),
        'sw' => array(
            'entrepreneur' => 'mfanyabiashara',
            'startup' => 'biashara mpya',
            'business_plan' => 'mpango wa biashara',
            'market_research' => 'utafiti wa soko',
            'funding' => 'fedha za uongozaji'
        ),
        'yo' => array(
            'entrepreneur' => 'onisowo',
            'startup' => 'iṣowo titun',
            'business_plan' => 'eto iṣowo',
            'market_research' => 'iwadi oja',
            'funding' => 'owo idoko'
        ),
        'tw' => array(
            'entrepreneur' => 'adwumawura',
            'startup' => 'adwuma foforo',
            'business_plan' => 'adwuma nhyehyɛe',
            'market_research' => 'gua mu nhwehwɛmu',
            'funding' => 'sika a wɔde ma'
        ),
        'zu' => array(
            'entrepreneur' => 'usomabhizinisi',
            'startup' => 'ibhizinisi elisha',
            'business_plan' => 'uhlelo lwebhizinisi',
            'market_research' => 'ucwaningo lwemakethe',
            'funding' => 'uxhaso lwezimali'
        )
    );
    
    return $terms[$language] ?? $terms['en'];
}

function chatgabi_localize_business_content($content, $language, $country = null) {
    $terms = chatgabi_get_cultural_business_terms($language, $country);
    
    foreach ($terms as $english => $local) {
        $content = str_ireplace($english, $local, $content);
    }
    
    return $content;
}
```

### **Task 3.3: African Examples Localization**

#### **Objective**: Add language support to African business examples

#### **Database Update:**
```sql
-- Add language fields to African examples
ALTER TABLE wp_chatgabi_african_examples 
ADD COLUMN language_code VARCHAR(5) DEFAULT 'en',
ADD COLUMN translated_content JSON COMMENT 'Multi-language example content';

-- Create index for language queries
ALTER TABLE wp_chatgabi_african_examples 
ADD INDEX idx_language_country (language_code, country);
```

## 📋 **Phase 4: Advanced Features (Week 7-8)**

### **Task 4.1: AI-Powered Translation**

#### **Objective**: Implement OpenAI-based translation for templates

#### **New File**: `inc/ai-translation.php`

#### **Implementation:**
```php
<?php
/**
 * AI-powered translation using OpenAI API
 */
function chatgabi_ai_translate_template($content, $target_language, $context = 'business') {
    // Ensure we stay within 400-token limit
    $max_content_length = 1500; // Approximate character limit for 400 tokens
    
    if (strlen($content) > $max_content_length) {
        // Split content into chunks
        return chatgabi_ai_translate_chunked($content, $target_language, $context);
    }
    
    $language_names = array(
        'sw' => 'Swahili',
        'yo' => 'Yoruba', 
        'tw' => 'Twi',
        'zu' => 'Zulu'
    );
    
    $target_language_name = $language_names[$target_language] ?? $target_language;
    
    $prompt = "Translate the following business template content to {$target_language_name}, maintaining business terminology accuracy and cultural appropriateness for African markets:\n\n{$content}";
    
    // Use existing OpenAI integration
    $response = chatgabi_call_openai_api($prompt, array(
        'max_tokens' => 400,
        'temperature' => 0.3 // Lower temperature for more consistent translations
    ));
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    return $response['choices'][0]['message']['content'] ?? $content;
}

function chatgabi_ai_translate_chunked($content, $target_language, $context) {
    // Split content into sentences
    $sentences = preg_split('/(?<=[.!?])\s+/', $content);
    $chunks = array();
    $current_chunk = '';
    
    foreach ($sentences as $sentence) {
        if (strlen($current_chunk . $sentence) > 1500) {
            if (!empty($current_chunk)) {
                $chunks[] = trim($current_chunk);
                $current_chunk = $sentence;
            } else {
                $chunks[] = $sentence; // Single sentence too long
            }
        } else {
            $current_chunk .= ' ' . $sentence;
        }
    }
    
    if (!empty($current_chunk)) {
        $chunks[] = trim($current_chunk);
    }
    
    $translated_chunks = array();
    foreach ($chunks as $chunk) {
        $translated = chatgabi_ai_translate_template($chunk, $target_language, $context);
        if (is_wp_error($translated)) {
            return $translated;
        }
        $translated_chunks[] = $translated;
    }
    
    return implode(' ', $translated_chunks);
}
```

### **Task 4.2: Real-Time Language Switching**

#### **Objective**: Implement AJAX-based language switching without page reload

#### **JavaScript Enhancement:**
```javascript
// Enhanced language switching
class ChatGABILanguageSwitcher {
    constructor() {
        this.currentLanguage = chatgabiConfig.userLanguage || 'en';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadLanguageStrings();
    }
    
    bindEvents() {
        document.addEventListener('change', (e) => {
            if (e.target.matches('.chatgabi-language-select')) {
                this.switchLanguage(e.target.value);
            }
        });
    }
    
    async switchLanguage(newLanguage) {
        try {
            // Save preference
            await this.saveLanguagePreference(newLanguage);
            
            // Update UI strings
            await this.updateUIStrings(newLanguage);
            
            // Update form placeholders
            this.updateFormElements(newLanguage);
            
            // Trigger custom event
            document.dispatchEvent(new CustomEvent('chatgabi:languageChanged', {
                detail: { language: newLanguage }
            }));
            
            this.currentLanguage = newLanguage;
            
        } catch (error) {
            console.error('Language switch failed:', error);
        }
    }
    
    async saveLanguagePreference(language) {
        const response = await fetch(chatgabiAjax.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'chatgabi_switch_language',
                language: language,
                nonce: chatgabiAjax.nonce
            })
        });
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.data || 'Failed to save language preference');
        }
    }
    
    async updateUIStrings(language) {
        // Load language strings via AJAX
        const response = await fetch(chatgabiAjax.restUrl + 'language-strings/' + language, {
            headers: {
                'X-WP-Nonce': chatgabiAjax.restNonce
            }
        });
        
        const strings = await response.json();
        
        // Update all elements with data-translate attribute
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            if (strings[key]) {
                element.textContent = strings[key];
            }
        });
    }
    
    updateFormElements(language) {
        // Update placeholders and labels
        const placeholders = {
            'en': {
                'chat-input': 'Ask me to help with your business needs...',
                'template-search': 'Search templates...'
            },
            'sw': {
                'chat-input': 'Niulize nisaidie mahitaji ya biashara yako...',
                'template-search': 'Tafuta violezo...'
            }
            // Add other languages
        };
        
        const langPlaceholders = placeholders[language] || placeholders['en'];
        
        Object.entries(langPlaceholders).forEach(([id, text]) => {
            const element = document.getElementById(id);
            if (element) {
                element.placeholder = text;
            }
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new ChatGABILanguageSwitcher();
});
```

## 🧪 **Testing Strategy**

### **Testing Phases:**

1. **Unit Testing**: Individual translation functions
2. **Integration Testing**: Language switching workflows  
3. **User Acceptance Testing**: African market user feedback
4. **Performance Testing**: Load time impact measurement
5. **Accessibility Testing**: Screen reader compatibility

### **Test Cases:**

1. **Text Domain Loading**: Verify all strings load correctly
2. **Language Switching**: Test real-time UI updates
3. **Currency Formatting**: Validate all African currencies
4. **Template Translation**: Check AI translation accuracy
5. **Performance Impact**: Measure <200ms additional load time

## 📊 **Success Metrics**

### **Technical Metrics:**
- **Translation Coverage**: 95% of UI elements
- **Load Time Impact**: <200ms additional
- **Error Rate**: <1% translation-related issues
- **Cache Hit Rate**: >80% for translations

### **User Metrics:**
- **Language Adoption**: 50% of users switch to local language
- **User Engagement**: 40% increase from African markets
- **Template Usage**: 25% improvement in usage rates
- **User Retention**: 15% improvement in African markets

---

**Implementation Timeline**: 8 weeks  
**Resource Allocation**: 130 development hours  
**Success Criteria**: Full multi-language support with <2s load time
