<?php
/**
 * Admin Interface for African Business Examples
 * WordPress admin interface for managing business case studies
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin menu for African Examples
 */
function chatgabi_add_african_examples_admin_menu() {
    add_submenu_page(
        'chatgabi',
        __('African Business Examples', 'chatgabi'),
        __('Business Examples', 'chatgabi'),
        'manage_options',
        'chatgabi-african-examples',
        'chatgabi_african_examples_admin_page'
    );
}
add_action('admin_menu', 'chatgabi_add_african_examples_admin_menu');

/**
 * Admin page for managing African business examples
 */
function chatgabi_african_examples_admin_page() {
    $examples_manager = chatgabi_get_african_examples_manager();
    
    // Handle form submissions
    if (isset($_POST['action'])) {
        check_admin_referer('chatgabi_african_examples_nonce');
        
        switch ($_POST['action']) {
            case 'add_example':
                chatgabi_handle_add_example();
                break;
            case 'update_example':
                chatgabi_handle_update_example();
                break;
            case 'delete_example':
                chatgabi_handle_delete_example();
                break;
            case 'initialize_sample_data':
                $result = $examples_manager->initialize_sample_data();
                if ($result) {
                    echo '<div class="notice notice-success"><p>Sample data initialized successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-info"><p>Sample data already exists.</p></div>';
                }
                break;
        }
    }
    
    // Get current action
    $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
    $example_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    ?>
    <div class="wrap">
        <h1><?php _e('African Business Examples', 'chatgabi'); ?></h1>
        
        <?php
        switch ($action) {
            case 'add':
                chatgabi_render_add_example_form();
                break;
            case 'edit':
                chatgabi_render_edit_example_form($example_id);
                break;
            case 'view':
                chatgabi_render_view_example($example_id);
                break;
            default:
                chatgabi_render_examples_list();
                break;
        }
        ?>
    </div>
    <?php
}

/**
 * Render examples list
 */
function chatgabi_render_examples_list() {
    $examples_manager = chatgabi_get_african_examples_manager();
    
    // Get filter parameters
    $country_filter = isset($_GET['country']) ? sanitize_text_field($_GET['country']) : '';
    $industry_filter = isset($_GET['industry']) ? sanitize_text_field($_GET['industry']) : '';
    $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
    
    // Get examples
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_african_examples';
    
    $where_conditions = array('1=1');
    $where_values = array();
    
    if (!empty($country_filter)) {
        $where_conditions[] = 'country = %s';
        $where_values[] = $country_filter;
    }
    
    if (!empty($industry_filter)) {
        $where_conditions[] = 'industry LIKE %s';
        $where_values[] = '%' . $industry_filter . '%';
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = 'status = %s';
        $where_values[] = $status_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT * FROM {$table_name} WHERE {$where_clause} ORDER BY created_at DESC";
    
    if (!empty($where_values)) {
        $examples = $wpdb->get_results($wpdb->prepare($sql, $where_values));
    } else {
        $examples = $wpdb->get_results($sql);
    }
    
    // Get statistics
    $stats = $examples_manager->get_statistics();
    ?>
    
    <div class="chatgabi-examples-header">
        <div class="chatgabi-stats-cards">
            <div class="stats-card">
                <h3><?php echo esc_html($stats['total']); ?></h3>
                <p>Total Examples</p>
            </div>
            <div class="stats-card">
                <h3><?php echo esc_html($stats['featured']); ?></h3>
                <p>Featured</p>
            </div>
            <div class="stats-card">
                <h3><?php echo esc_html($stats['verified']); ?></h3>
                <p>Verified</p>
            </div>
        </div>
        
        <div class="chatgabi-actions">
            <a href="<?php echo admin_url('admin.php?page=chatgabi-african-examples&action=add'); ?>" 
               class="button button-primary">Add New Example</a>
            
            <form method="post" style="display: inline;">
                <?php wp_nonce_field('chatgabi_african_examples_nonce'); ?>
                <input type="hidden" name="action" value="initialize_sample_data">
                <button type="submit" class="button" 
                        onclick="return confirm('This will add sample business examples. Continue?')">
                    Initialize Sample Data
                </button>
            </form>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="chatgabi-filters">
        <form method="get">
            <input type="hidden" name="page" value="chatgabi-african-examples">
            
            <select name="country">
                <option value="">All Countries</option>
                <option value="GH" <?php selected($country_filter, 'GH'); ?>>Ghana</option>
                <option value="KE" <?php selected($country_filter, 'KE'); ?>>Kenya</option>
                <option value="NG" <?php selected($country_filter, 'NG'); ?>>Nigeria</option>
                <option value="ZA" <?php selected($country_filter, 'ZA'); ?>>South Africa</option>
            </select>
            
            <input type="text" name="industry" placeholder="Industry" 
                   value="<?php echo esc_attr($industry_filter); ?>">
            
            <select name="status">
                <option value="">All Status</option>
                <option value="active" <?php selected($status_filter, 'active'); ?>>Active</option>
                <option value="pending" <?php selected($status_filter, 'pending'); ?>>Pending</option>
                <option value="inactive" <?php selected($status_filter, 'inactive'); ?>>Inactive</option>
            </select>
            
            <button type="submit" class="button">Filter</button>
            <a href="<?php echo admin_url('admin.php?page=chatgabi-african-examples'); ?>" 
               class="button">Clear</a>
        </form>
    </div>
    
    <!-- Examples Table -->
    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th>Company</th>
                <th>Country</th>
                <th>Industry</th>
                <th>Type</th>
                <th>Year</th>
                <th>Status</th>
                <th>Featured</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($examples)): ?>
                <tr>
                    <td colspan="8">
                        <p>No business examples found. 
                           <a href="<?php echo admin_url('admin.php?page=chatgabi-african-examples&action=add'); ?>">
                               Add your first example
                           </a> or initialize sample data.
                        </p>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($examples as $example): ?>
                    <tr>
                        <td>
                            <strong><?php echo esc_html($example->company_name); ?></strong>
                            <?php if ($example->is_verified): ?>
                                <span class="dashicons dashicons-yes-alt" style="color: green;" 
                                      title="Verified"></span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo chatgabi_get_country_name($example->country); ?></td>
                        <td><?php echo esc_html($example->industry); ?></td>
                        <td><?php echo esc_html(ucfirst($example->business_type)); ?></td>
                        <td><?php echo esc_html($example->year); ?></td>
                        <td>
                            <span class="status-<?php echo esc_attr($example->status); ?>">
                                <?php echo esc_html(ucfirst($example->status)); ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($example->is_featured): ?>
                                <span class="dashicons dashicons-star-filled" style="color: gold;"></span>
                            <?php else: ?>
                                <span class="dashicons dashicons-star-empty"></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=chatgabi-african-examples&action=view&id=' . $example->id); ?>" 
                               class="button button-small">View</a>
                            <a href="<?php echo admin_url('admin.php?page=chatgabi-african-examples&action=edit&id=' . $example->id); ?>" 
                               class="button button-small">Edit</a>
                            <form method="post" style="display: inline;">
                                <?php wp_nonce_field('chatgabi_african_examples_nonce'); ?>
                                <input type="hidden" name="action" value="delete_example">
                                <input type="hidden" name="example_id" value="<?php echo esc_attr($example->id); ?>">
                                <button type="submit" class="button button-small button-link-delete" 
                                        onclick="return confirm('Are you sure you want to delete this example?')">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <style>
    .chatgabi-examples-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .chatgabi-stats-cards {
        display: flex;
        gap: 20px;
    }
    
    .stats-card {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        text-align: center;
        min-width: 100px;
    }
    
    .stats-card h3 {
        margin: 0;
        font-size: 24px;
        color: #0073aa;
    }
    
    .stats-card p {
        margin: 5px 0 0 0;
        color: #666;
    }
    
    .chatgabi-filters {
        background: #f9f9f9;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 20px;
    }
    
    .chatgabi-filters form {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .status-active { color: green; font-weight: bold; }
    .status-pending { color: orange; font-weight: bold; }
    .status-inactive { color: red; font-weight: bold; }
    </style>
    <?php
}

/**
 * Handle add example form submission
 */
function chatgabi_handle_add_example() {
    $examples_manager = chatgabi_get_african_examples_manager();

    $data = array(
        'company_name' => sanitize_text_field($_POST['company_name']),
        'country' => sanitize_text_field($_POST['country']),
        'industry' => sanitize_text_field($_POST['industry']),
        'business_type' => sanitize_text_field($_POST['business_type']),
        'success_story' => wp_kses_post($_POST['success_story']),
        'metrics' => sanitize_textarea_field($_POST['metrics']),
        'year' => intval($_POST['year']),
        'revenue_range' => sanitize_text_field($_POST['revenue_range']),
        'employee_count' => sanitize_text_field($_POST['employee_count']),
        'funding_stage' => sanitize_text_field($_POST['funding_stage']),
        'key_achievements' => sanitize_textarea_field($_POST['key_achievements']),
        'challenges_overcome' => sanitize_textarea_field($_POST['challenges_overcome']),
        'lessons_learned' => sanitize_textarea_field($_POST['lessons_learned']),
        'contact_info' => sanitize_textarea_field($_POST['contact_info']),
        'website_url' => esc_url_raw($_POST['website_url']),
        'logo_url' => esc_url_raw($_POST['logo_url']),
        'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
        'is_verified' => isset($_POST['is_verified']) ? 1 : 0,
        'status' => sanitize_text_field($_POST['status'])
    );

    $result = $examples_manager->add_example($data);

    if (is_wp_error($result)) {
        echo '<div class="notice notice-error"><p>Error: ' . $result->get_error_message() . '</p></div>';
    } else {
        echo '<div class="notice notice-success"><p>Business example added successfully!</p></div>';
        // Redirect to list
        wp_redirect(admin_url('admin.php?page=chatgabi-african-examples'));
        exit;
    }
}

/**
 * Handle update example form submission
 */
function chatgabi_handle_update_example() {
    $examples_manager = chatgabi_get_african_examples_manager();
    $example_id = intval($_POST['example_id']);

    $data = array(
        'company_name' => sanitize_text_field($_POST['company_name']),
        'country' => sanitize_text_field($_POST['country']),
        'industry' => sanitize_text_field($_POST['industry']),
        'business_type' => sanitize_text_field($_POST['business_type']),
        'success_story' => wp_kses_post($_POST['success_story']),
        'metrics' => sanitize_textarea_field($_POST['metrics']),
        'year' => intval($_POST['year']),
        'revenue_range' => sanitize_text_field($_POST['revenue_range']),
        'employee_count' => sanitize_text_field($_POST['employee_count']),
        'funding_stage' => sanitize_text_field($_POST['funding_stage']),
        'key_achievements' => sanitize_textarea_field($_POST['key_achievements']),
        'challenges_overcome' => sanitize_textarea_field($_POST['challenges_overcome']),
        'lessons_learned' => sanitize_textarea_field($_POST['lessons_learned']),
        'contact_info' => sanitize_textarea_field($_POST['contact_info']),
        'website_url' => esc_url_raw($_POST['website_url']),
        'logo_url' => esc_url_raw($_POST['logo_url']),
        'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
        'is_verified' => isset($_POST['is_verified']) ? 1 : 0,
        'status' => sanitize_text_field($_POST['status'])
    );

    $result = $examples_manager->update_example($example_id, $data);

    if ($result) {
        echo '<div class="notice notice-success"><p>Business example updated successfully!</p></div>';
        // Redirect to list
        wp_redirect(admin_url('admin.php?page=chatgabi-african-examples'));
        exit;
    } else {
        echo '<div class="notice notice-error"><p>Failed to update business example.</p></div>';
    }
}

/**
 * Handle delete example
 */
function chatgabi_handle_delete_example() {
    $examples_manager = chatgabi_get_african_examples_manager();
    $example_id = intval($_POST['example_id']);

    $result = $examples_manager->delete_example($example_id);

    if ($result) {
        echo '<div class="notice notice-success"><p>Business example deleted successfully!</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>Failed to delete business example.</p></div>';
    }
}

/**
 * Get country name from code
 */
function chatgabi_get_country_name($country_code) {
    $countries = array(
        'GH' => 'Ghana',
        'KE' => 'Kenya',
        'NG' => 'Nigeria',
        'ZA' => 'South Africa'
    );

    return isset($countries[$country_code]) ? $countries[$country_code] : $country_code;
}
