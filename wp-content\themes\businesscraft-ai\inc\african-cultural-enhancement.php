<?php
/**
 * African Cultural Context Enhancement System for ChatGABI
 * 
 * This file provides comprehensive cultural context integration including
 * traditional business practices, cultural values, and local market intelligence.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_African_Cultural_Enhancement {
    
    private $cultural_frameworks;
    private $traditional_practices;
    private $business_etiquette;
    private $success_philosophies;
    
    public function __construct() {
        $this->init_cultural_frameworks();
        $this->init_traditional_practices();
        $this->init_business_etiquette();
        $this->init_success_philosophies();
    }
    
    /**
     * Initialize cultural frameworks for each African region
     */
    private function init_cultural_frameworks() {
        $this->cultural_frameworks = array(
            'GH' => array(
                'primary_philosophy' => 'Sankofa',
                'description' => 'Learning from the past to build a better future',
                'business_application' => 'Combining traditional wisdom with modern business practices',
                'core_values' => array(
                    'respect_for_elders' => 'Seeking guidance from experienced business leaders',
                    'community_first' => 'Business success should benefit the entire community',
                    'patience_and_persistence' => 'Long-term thinking and gradual growth',
                    'honesty_and_integrity' => 'Transparent business dealings and trustworthiness'
                ),
                'decision_making' => array(
                    'consultation_process' => 'Seek advice from elders and community leaders',
                    'consensus_building' => 'Ensure community support for business decisions',
                    'traditional_wisdom' => 'Apply proverbs and traditional knowledge to modern challenges',
                    'collective_benefit' => 'Consider how decisions affect the broader community'
                ),
                'traditional_proverbs' => array(
                    'Se wo were fi na wosankofa a yenkyi' => 'It is not wrong to go back for that which you have forgotten',
                    'Obi nkyere abofra Nyame' => 'No one teaches a child about God (wisdom comes naturally)',
                    'Sɛ wo yɛ bia a, wo ani gye wo ho' => 'If you are patient, you will be satisfied'
                ),
                'business_greetings' => array(
                    'morning' => 'Maakye (Good morning)',
                    'afternoon' => 'Maaha (Good afternoon)', 
                    'business_inquiry' => 'Wo adwuma te sɛn? (How is your business?)',
                    'success_wish' => 'Nyame nhyira wo (May God bless you)'
                )
            ),
            'KE' => array(
                'primary_philosophy' => 'Harambee',
                'description' => 'Collective effort and mutual assistance for progress',
                'business_application' => 'Collaborative innovation and community-driven solutions',
                'core_values' => array(
                    'innovation_mindset' => 'Embracing new technologies and creative solutions',
                    'collective_progress' => 'Working together for mutual advancement',
                    'education_focus' => 'Continuous learning and skill development',
                    'efficiency_orientation' => 'Optimizing processes and maximizing impact'
                ),
                'decision_making' => array(
                    'collaborative_approach' => 'Involve stakeholders in planning and execution',
                    'data_driven' => 'Use evidence and metrics to guide decisions',
                    'innovation_focus' => 'Prioritize creative and technological solutions',
                    'scalability_thinking' => 'Consider regional and continental expansion'
                ),
                'traditional_proverbs' => array(
                    'Harambee' => 'Let us all pull together',
                    'Mti hauendi ila kwa upepo' => 'A tree does not fall by the wind alone',
                    'Kidole kimoja hakivunji chawa' => 'One finger cannot kill a louse'
                ),
                'business_greetings' => array(
                    'morning' => 'Hujambo (Good morning)',
                    'afternoon' => 'Habari za mchana (Good afternoon)',
                    'business_inquiry' => 'Habari za biashara? (How is business?)',
                    'success_wish' => 'Mungu akubariki (May God bless you)'
                )
            ),
            'NG' => array(
                'primary_philosophy' => 'Omolúàbí',
                'description' => 'Good character, integrity, and excellence in all endeavors',
                'business_application' => 'Building businesses with strong ethical foundations and ambitious goals',
                'core_values' => array(
                    'excellence_pursuit' => 'Striving for the highest standards in all activities',
                    'respect_for_hierarchy' => 'Acknowledging authority and seniority in business',
                    'family_advancement' => 'Business success should elevate family status',
                    'bold_vision' => 'Thinking big and pursuing ambitious goals'
                ),
                'decision_making' => array(
                    'elder_consultation' => 'Seek wisdom from experienced family and business elders',
                    'ambitious_planning' => 'Set bold goals and create comprehensive strategies',
                    'network_leverage' => 'Utilize extended family and ethnic networks',
                    'reputation_focus' => 'Consider impact on personal and family reputation'
                ),
                'traditional_proverbs' => array(
                    'Bi a ba n gun igi, ko le gun eyin aja' => 'One who climbs a tree cannot climb the back of a dog',
                    'Eniyan ni aso mi' => 'People are my clothing (relationships matter most)',
                    'Iwa l\'ewa' => 'Character is beauty'
                ),
                'business_greetings' => array(
                    'morning' => 'Ẹ kú àárọ̀ (Good morning)',
                    'afternoon' => 'Ẹ kú ọ̀sán (Good afternoon)',
                    'business_inquiry' => 'Báwo ni iṣẹ́? (How is work/business?)',
                    'success_wish' => 'Kí Ọlọ́run bùkún ẹ (May God bless you)'
                )
            ),
            'ZA' => array(
                'primary_philosophy' => 'Ubuntu',
                'description' => 'I am because we are - interconnectedness and shared humanity',
                'business_application' => 'Building inclusive businesses that uplift entire communities',
                'core_values' => array(
                    'interconnectedness' => 'Recognizing that individual success depends on community success',
                    'quality_standards' => 'Maintaining high professional and ethical standards',
                    'transformation_focus' => 'Contributing to economic transformation and empowerment',
                    'social_responsibility' => 'Businesses have obligations to society'
                ),
                'decision_making' => array(
                    'consensus_building' => 'Seek agreement and buy-in from all stakeholders',
                    'community_impact' => 'Consider how decisions affect the broader community',
                    'transformation_alignment' => 'Ensure decisions support economic transformation',
                    'quality_assurance' => 'Maintain high standards in all business activities'
                ),
                'traditional_proverbs' => array(
                    'Umuntu ngumuntu ngabantu' => 'A person is a person through other people',
                    'Ukuphila kuyabonakala' => 'Life is evident/visible',
                    'Indlela ibuzwa kwabaphambili' => 'The way is asked from those who have traveled it'
                ),
                'business_greetings' => array(
                    'morning' => 'Sawubona (Good morning)',
                    'afternoon' => 'Sanibonani (Good afternoon)',
                    'business_inquiry' => 'Kunjani ibhizinisi? (How is business?)',
                    'success_wish' => 'Nginikufisela impumelelo (I wish you success)'
                )
            )
        );
    }
    
    /**
     * Initialize traditional business practices
     */
    private function init_traditional_practices() {
        $this->traditional_practices = array(
            'GH' => array(
                'market_systems' => array(
                    'traditional_markets' => 'Kejetia Market (Kumasi), Makola Market (Accra) - relationship-based trading',
                    'credit_systems' => 'Susu (rotating savings), community lending circles',
                    'negotiation_style' => 'Patient, relationship-focused, respect for age and experience',
                    'payment_methods' => 'Mobile money integration with traditional cash systems'
                ),
                'business_ceremonies' => array(
                    'business_blessing' => 'Seeking traditional blessing for new ventures',
                    'naming_ceremonies' => 'Community involvement in business naming',
                    'success_celebrations' => 'Sharing success with community through festivals'
                ),
                'seasonal_considerations' => array(
                    'farming_seasons' => 'Align business activities with agricultural cycles',
                    'festival_periods' => 'Homowo, Aboakyir - peak business and celebration times',
                    'harmattan_season' => 'Dry season business adjustments'
                )
            ),
            'KE' => array(
                'market_systems' => array(
                    'traditional_markets' => 'Gikomba Market (Nairobi), Muthurwa Market - innovation hubs',
                    'credit_systems' => 'Chama groups, table banking, M-Shwari integration',
                    'negotiation_style' => 'Direct, efficiency-focused, technology-enabled',
                    'payment_methods' => 'M-Pesa dominant, digital-first approach'
                ),
                'business_ceremonies' => array(
                    'harambee_events' => 'Community fundraising for business ventures',
                    'innovation_showcases' => 'Tech demo days and startup pitches',
                    'mentorship_circles' => 'Elder-youth business knowledge transfer'
                ),
                'seasonal_considerations' => array(
                    'agricultural_cycles' => 'Coffee and tea harvest seasons affect rural markets',
                    'school_terms' => 'Education-related business opportunities',
                    'tourism_seasons' => 'Safari and beach tourism peak periods'
                )
            ),
            'NG' => array(
                'market_systems' => array(
                    'traditional_markets' => 'Alaba Market (Lagos), Onitsha Main Market - massive trade hubs',
                    'credit_systems' => 'Esusu (rotating credit), family funding networks',
                    'negotiation_style' => 'Bold, relationship-based, status-conscious',
                    'payment_methods' => 'Bank transfers, fintech solutions, cash prevalent'
                ),
                'business_ceremonies' => array(
                    'business_launches' => 'Grand opening ceremonies with community leaders',
                    'success_celebrations' => 'Elaborate parties to showcase achievement',
                    'naming_ceremonies' => 'Traditional blessing of business names'
                ),
                'seasonal_considerations' => array(
                    'rainy_seasons' => 'Transportation and logistics challenges',
                    'festive_periods' => 'Christmas, Eid - peak consumer spending',
                    'harvest_seasons' => 'Agricultural product availability and pricing'
                )
            ),
            'ZA' => array(
                'market_systems' => array(
                    'traditional_markets' => 'Warwick Junction (Durban), taxi rank economies',
                    'credit_systems' => 'Stokvels (savings clubs), community investment schemes',
                    'negotiation_style' => 'Professional, consensus-seeking, quality-focused',
                    'payment_methods' => 'EFT, card payments, mobile solutions growing'
                ),
                'business_ceremonies' => array(
                    'ubuntu_circles' => 'Community business support meetings',
                    'transformation_events' => 'B-BBEE and empowerment celebrations',
                    'heritage_integration' => 'Incorporating cultural heritage in business'
                ),
                'seasonal_considerations' => array(
                    'holiday_seasons' => 'December holidays - major consumer period',
                    'school_holidays' => 'Family-focused business opportunities',
                    'heritage_month' => 'September - cultural business promotion'
                )
            )
        );
    }
    
    /**
     * Initialize business etiquette guidelines
     */
    private function init_business_etiquette() {
        $this->business_etiquette = array(
            'GH' => array(
                'meeting_protocols' => array(
                    'greetings' => 'Extended greetings, ask about family and health',
                    'seating' => 'Respect age hierarchy in seating arrangements',
                    'timing' => 'Allow extra time for relationship building',
                    'decision_making' => 'Consensus-seeking, elder consultation'
                ),
                'communication_style' => array(
                    'directness' => 'Indirect communication, avoid confrontation',
                    'respect_language' => 'Use titles and honorifics appropriately',
                    'listening' => 'Active listening and patience highly valued',
                    'feedback' => 'Constructive, private feedback preferred'
                ),
                'gift_giving' => array(
                    'appropriate_gifts' => 'Kente cloth, local crafts, books',
                    'occasions' => 'Business openings, successful deals, festivals',
                    'presentation' => 'Present with both hands, show respect'
                )
            ),
            'KE' => array(
                'meeting_protocols' => array(
                    'greetings' => 'Professional handshakes, business card exchange',
                    'punctuality' => 'Respect for time, arrive on schedule',
                    'agenda_focus' => 'Structured meetings with clear objectives',
                    'technology_use' => 'Embrace digital tools and presentations'
                ),
                'communication_style' => array(
                    'directness' => 'Direct, clear communication appreciated',
                    'efficiency' => 'Value time-saving and productive discussions',
                    'innovation_talk' => 'Discuss technology and innovation opportunities',
                    'data_focus' => 'Support arguments with facts and figures'
                ),
                'networking' => array(
                    'professional_events' => 'Tech meetups, business forums, innovation hubs',
                    'online_presence' => 'LinkedIn, Twitter, professional networks',
                    'mentorship' => 'Seek and offer mentorship opportunities'
                )
            ),
            'NG' => array(
                'meeting_protocols' => array(
                    'greetings' => 'Warm, extended greetings with status recognition',
                    'hierarchy_respect' => 'Acknowledge seniority and titles',
                    'relationship_time' => 'Invest time in personal relationship building',
                    'grand_gestures' => 'Appreciate impressive presentations and venues'
                ),
                'communication_style' => array(
                    'confidence' => 'Bold, confident communication style',
                    'storytelling' => 'Use narratives and examples to illustrate points',
                    'achievement_focus' => 'Highlight successes and ambitious goals',
                    'network_mentions' => 'Reference mutual connections and relationships'
                ),
                'business_entertainment' => array(
                    'dining' => 'Business meals important for relationship building',
                    'celebrations' => 'Celebrate successes with style and flair',
                    'cultural_events' => 'Attend cultural and religious celebrations'
                )
            ),
            'ZA' => array(
                'meeting_protocols' => array(
                    'professionalism' => 'High professional standards expected',
                    'preparation' => 'Thorough preparation and documentation',
                    'consensus_building' => 'Seek input from all stakeholders',
                    'transformation_awareness' => 'Consider B-BBEE and transformation goals'
                ),
                'communication_style' => array(
                    'quality_focus' => 'Emphasize quality and excellence',
                    'detail_orientation' => 'Provide comprehensive information',
                    'social_impact' => 'Discuss community and social benefits',
                    'compliance_awareness' => 'Address regulatory and compliance issues'
                ),
                'diversity_inclusion' => array(
                    'language_sensitivity' => 'Respect for multiple languages and cultures',
                    'inclusive_practices' => 'Ensure all voices are heard and valued',
                    'transformation_support' => 'Support economic transformation initiatives'
                )
            )
        );
    }
    
    /**
     * Initialize success philosophies
     */
    private function init_success_philosophies() {
        $this->success_philosophies = array(
            'GH' => array(
                'definition_of_success' => 'Community prosperity, family advancement, and sustainable growth',
                'success_metrics' => array(
                    'community_impact' => 'Number of people positively affected',
                    'family_advancement' => 'Improvement in family living standards',
                    'business_sustainability' => 'Long-term viability and growth',
                    'cultural_preservation' => 'Maintaining traditional values while innovating'
                ),
                'success_stories' => array(
                    'local_heroes' => 'Nana Akufo-Addo (political leadership), Rebecca Akufo-Addo (social impact)',
                    'business_leaders' => 'Aliko Dangote (regional expansion), Kwame Despite (media empire)',
                    'social_entrepreneurs' => 'Bright Simons (innovation), Sangu Delle (venture capital)'
                )
            ),
            'KE' => array(
                'definition_of_success' => 'Innovation impact, scalable solutions, and regional leadership',
                'success_metrics' => array(
                    'innovation_index' => 'Technology adoption and creation',
                    'scalability_factor' => 'Ability to expand across East Africa',
                    'efficiency_gains' => 'Process improvements and cost reductions',
                    'knowledge_transfer' => 'Skills development and education impact'
                ),
                'success_stories' => array(
                    'tech_pioneers' => 'Michael Joseph (M-Pesa), Ory Okolloh (Ushahidi)',
                    'business_innovators' => 'James Mwangi (Equity Bank), Vimal Shah (Bidco)',
                    'social_entrepreneurs' => 'Jessica Colaco (InMobi), Juliana Rotich (BRCK)'
                )
            ),
            'NG' => array(
                'definition_of_success' => 'Wealth creation, family elevation, and continental influence',
                'success_metrics' => array(
                    'wealth_accumulation' => 'Financial growth and asset building',
                    'family_status' => 'Elevation of family social and economic position',
                    'market_dominance' => 'Leadership position in chosen industry',
                    'continental_reach' => 'Expansion across African markets'
                ),
                'success_stories' => array(
                    'business_titans' => 'Aliko Dangote (manufacturing), Tony Elumelu (banking)',
                    'tech_leaders' => 'Shola Akinlade (Paystack), Olugbenga Agboola (Flutterwave)',
                    'entertainment_moguls' => 'Mo Abudu (media), Don Jazzy (music)'
                )
            ),
            'ZA' => array(
                'definition_of_success' => 'Quality excellence, community upliftment, and transformation leadership',
                'success_metrics' => array(
                    'quality_standards' => 'International-level quality and professionalism',
                    'transformation_impact' => 'Contribution to economic transformation',
                    'community_development' => 'Upliftment of disadvantaged communities',
                    'continental_gateway' => 'Facilitating African market access'
                ),
                'success_stories' => array(
                    'transformation_leaders' => 'Patrice Motsepe (mining), Cyril Ramaphosa (business/politics)',
                    'innovation_pioneers' => 'Mark Shuttleworth (technology), Vinny Lingham (blockchain)',
                    'social_entrepreneurs' => 'Taddy Blecher (education), Rapelang Rabana (technology)'
                )
            )
        );
    }
    
    /**
     * Get cultural context for a specific country and business scenario
     */
    public function get_cultural_context($country_code, $business_scenario = 'general') {
        $framework = $this->cultural_frameworks[$country_code] ?? $this->cultural_frameworks['GH'];
        $practices = $this->traditional_practices[$country_code] ?? $this->traditional_practices['GH'];
        $etiquette = $this->business_etiquette[$country_code] ?? $this->business_etiquette['GH'];
        $philosophy = $this->success_philosophies[$country_code] ?? $this->success_philosophies['GH'];
        
        return array(
            'cultural_framework' => $framework,
            'traditional_practices' => $practices,
            'business_etiquette' => $etiquette,
            'success_philosophy' => $philosophy,
            'country_code' => $country_code,
            'business_scenario' => $business_scenario
        );
    }
    
    /**
     * Apply cultural enhancement to business content
     */
    public function enhance_business_content($content, $country_code, $content_type = 'general') {
        $cultural_context = $this->get_cultural_context($country_code, $content_type);
        
        // Add cultural framework
        $enhanced_content = $this->add_cultural_framework($content, $cultural_context['cultural_framework']);
        
        // Add traditional practices
        $enhanced_content = $this->add_traditional_practices($enhanced_content, $cultural_context['traditional_practices']);
        
        // Add business etiquette
        $enhanced_content = $this->add_business_etiquette($enhanced_content, $cultural_context['business_etiquette']);
        
        // Add success philosophy
        $enhanced_content = $this->add_success_philosophy($enhanced_content, $cultural_context['success_philosophy']);
        
        return $enhanced_content;
    }
    
    /**
     * Add cultural framework to content
     */
    private function add_cultural_framework($content, $framework) {
        $content .= "\n\n## " . __('Cultural Business Framework', 'chatgabi') . "\n";
        $content .= "**" . __('Philosophy', 'chatgabi') . "**: " . $framework['primary_philosophy'] . " - " . $framework['description'] . "\n";
        $content .= "**" . __('Business Application', 'chatgabi') . "**: " . $framework['business_application'] . "\n\n";
        
        $content .= "### " . __('Core Values', 'chatgabi') . "\n";
        foreach ($framework['core_values'] as $value => $description) {
            $content .= "- **" . ucwords(str_replace('_', ' ', $value)) . "**: " . $description . "\n";
        }
        
        return $content;
    }
    
    /**
     * Add traditional practices to content
     */
    private function add_traditional_practices($content, $practices) {
        $content .= "\n\n### " . __('Traditional Business Practices', 'chatgabi') . "\n";
        
        if (isset($practices['market_systems'])) {
            $content .= "**" . __('Market Systems', 'chatgabi') . "**: " . $practices['market_systems']['traditional_markets'] . "\n";
            $content .= "**" . __('Credit Systems', 'chatgabi') . "**: " . $practices['market_systems']['credit_systems'] . "\n";
        }
        
        return $content;
    }
    
    /**
     * Add business etiquette to content
     */
    private function add_business_etiquette($content, $etiquette) {
        $content .= "\n\n### " . __('Business Etiquette Guidelines', 'chatgabi') . "\n";
        
        if (isset($etiquette['meeting_protocols'])) {
            $content .= "**" . __('Meeting Protocols', 'chatgabi') . "**: ";
            $protocols = array();
            foreach ($etiquette['meeting_protocols'] as $key => $value) {
                $protocols[] = ucwords(str_replace('_', ' ', $key)) . " - " . $value;
            }
            $content .= implode('; ', $protocols) . "\n";
        }
        
        return $content;
    }
    
    /**
     * Add success philosophy to content
     */
    private function add_success_philosophy($content, $philosophy) {
        $content .= "\n\n### " . __('Success Philosophy', 'chatgabi') . "\n";
        $content .= "**" . __('Definition of Success', 'chatgabi') . "**: " . $philosophy['definition_of_success'] . "\n\n";
        
        $content .= "**" . __('Success Metrics', 'chatgabi') . "**:\n";
        foreach ($philosophy['success_metrics'] as $metric => $description) {
            $content .= "- " . ucwords(str_replace('_', ' ', $metric)) . ": " . $description . "\n";
        }
        
        return $content;
    }
}

// Initialize the cultural enhancement system
function chatgabi_get_african_cultural_enhancement() {
    static $enhancement = null;
    
    if ($enhancement === null) {
        $enhancement = new ChatGABI_African_Cultural_Enhancement();
    }
    
    return $enhancement;
}
?>
