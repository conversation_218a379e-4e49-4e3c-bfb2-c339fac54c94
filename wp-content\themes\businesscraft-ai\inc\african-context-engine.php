<?php
/**
 * African Context Engine for BusinessCraft AI
 * Injects African business knowledge and cultural context into AI responses
 *
 * @package BusinessCraft_AI
 * @since 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_African_Context_Engine {

    private $country_contexts;
    private $business_contexts;
    private $cultural_frameworks;

    public function __construct() {
        $this->init_contexts();
    }

    /**
     * Initialize African context data
     */
    private function init_contexts() {
        $this->country_contexts = array(
            'GH' => array(
                'business_culture' => 'Respectful, community-oriented, value-driven approach. Emphasis on relationships and trust-building.',
                'communication_style' => 'Formal yet warm, with emphasis on respect for hierarchy and community values.',
                'market_characteristics' => 'Growing digital adoption, mobile-first economy, strong traditional business practices.',
                'regulatory_environment' => 'Business registration through Registrar General, VAT registration at 15%, corporate tax at 25%.',
                'payment_preferences' => 'MTN Mobile Money, Vodafone Cash, bank transfers, cash transactions still common.',
                'business_challenges' => 'Power supply inconsistency, access to capital, digital literacy gaps.',
                'opportunities' => 'Growing middle class, increasing smartphone penetration, government digitization initiatives.',
                'key_industries' => 'Agriculture, mining, oil & gas, manufacturing, services, technology.',
                'business_formality' => 'Mix of formal and informal sectors, emphasis on business registration and compliance.',
                'networking_culture' => 'Strong emphasis on personal relationships, community connections, and referrals.'
            ),
            'KE' => array(
                'business_culture' => 'Innovation-driven, tech-savvy, entrepreneurial mindset with global outlook.',
                'communication_style' => 'Direct, practical, efficiency-focused with professional approach.',
                'market_characteristics' => 'Leading fintech adoption, strong startup ecosystem, digital-first approach.',
                'regulatory_environment' => 'Business registration via eCitizen portal, VAT at 16%, corporate tax at 30%.',
                'payment_preferences' => 'M-Pesa dominant, Airtel Money, bank transfers, card payments growing.',
                'business_challenges' => 'Access to funding, market competition, regulatory compliance.',
                'opportunities' => 'Tech hub status, strong mobile money ecosystem, regional market access.',
                'key_industries' => 'Technology, agriculture, tourism, manufacturing, financial services.',
                'business_formality' => 'High formalization rate, strong compliance culture, digital-first processes.',
                'networking_culture' => 'Tech meetups, innovation hubs, professional associations, online communities.'
            ),
            'NG' => array(
                'business_culture' => 'Ambitious, globally connected, solution-oriented with strong entrepreneurial drive.',
                'communication_style' => 'Bold, confident, value-driven with clear benefits articulation.',
                'market_characteristics' => 'Largest African economy, diverse market, high competition, price sensitivity.',
                'regulatory_environment' => 'CAC registration, VAT at 7.5%, company income tax at 30%, multiple compliance requirements.',
                'payment_preferences' => 'Bank transfers, Paystack, Flutterwave, mobile money growing, cash prevalent.',
                'business_challenges' => 'Infrastructure gaps, currency fluctuations, regulatory complexity, power supply.',
                'opportunities' => 'Large domestic market, growing tech sector, increasing digital adoption.',
                'key_industries' => 'Oil & gas, agriculture, technology, manufacturing, entertainment, financial services.',
                'business_formality' => 'Mixed formal/informal economy, increasing formalization efforts.',
                'networking_culture' => 'Business associations, industry groups, social media communities, trade events.'
            ),
            'ZA' => array(
                'business_culture' => 'Professional, quality-conscious, compliance-focused with established business practices.',
                'communication_style' => 'Professional, polished, detail-oriented with supporting evidence.',
                'market_characteristics' => 'Most developed African market, sophisticated financial system, high standards.',
                'regulatory_environment' => 'CIPC registration, VAT at 15%, corporate tax at 28%, strict compliance requirements.',
                'payment_preferences' => 'EFT, card payments, Yoco, SnapScan, traditional banking strong.',
                'business_challenges' => 'Economic uncertainty, high unemployment, regulatory complexity, skills gaps.',
                'opportunities' => 'Gateway to African markets, strong infrastructure, established business ecosystem.',
                'key_industries' => 'Mining, manufacturing, financial services, technology, agriculture, tourism.',
                'business_formality' => 'Highly formalized economy, strong regulatory compliance culture.',
                'networking_culture' => 'Professional associations, chambers of commerce, industry conferences, LinkedIn.'
            )
        );

        $this->business_contexts = array(
            'startup' => array(
                'focus_areas' => 'MVP development, market validation, funding strategies, team building',
                'common_challenges' => 'Access to capital, market entry, regulatory compliance, talent acquisition',
                'success_metrics' => 'User acquisition, revenue growth, market penetration, funding milestones',
                'african_considerations' => 'Local market adaptation, mobile-first approach, payment integration'
            ),
            'sme' => array(
                'focus_areas' => 'Operations optimization, market expansion, digital transformation, compliance',
                'common_challenges' => 'Cash flow management, competition, digital adoption, regulatory changes',
                'success_metrics' => 'Revenue growth, profit margins, market share, operational efficiency',
                'african_considerations' => 'Local supplier networks, community engagement, traditional business practices'
            ),
            'enterprise' => array(
                'focus_areas' => 'Strategic planning, market leadership, innovation, sustainability',
                'common_challenges' => 'Market volatility, regulatory compliance, digital transformation, talent retention',
                'success_metrics' => 'Market leadership, profitability, sustainability goals, stakeholder value',
                'african_considerations' => 'Regional expansion, local partnerships, social impact, governance'
            )
        );

        $this->cultural_frameworks = array(
            'communication_principles' => array(
                'respect_hierarchy' => 'Acknowledge seniority and authority in business contexts',
                'community_focus' => 'Emphasize collective benefits and community impact',
                'relationship_building' => 'Prioritize long-term relationships over quick transactions',
                'cultural_sensitivity' => 'Respect local customs, traditions, and business practices'
            ),
            'business_values' => array(
                'ubuntu_philosophy' => 'Interconnectedness and mutual support in business relationships',
                'sustainability' => 'Long-term thinking and environmental consciousness',
                'innovation_with_tradition' => 'Balancing modern approaches with traditional wisdom',
                'social_impact' => 'Business success measured by community benefit'
            )
        );
    }

    /**
     * Generate contextual prompt prefix based on user location and business type
     */
    public function generate_context_prompt($user_country, $business_type = 'sme', $industry = null) {
        $country_context = $this->country_contexts[$user_country] ?? $this->country_contexts['GH'];
        $business_context = $this->business_contexts[$business_type] ?? $this->business_contexts['sme'];

        $context_prompt = "You are BusinessCraft AI, an expert business assistant specializing in African markets. ";
        $context_prompt .= "You have deep knowledge of {$this->get_country_name($user_country)} business environment. ";

        // Add country-specific context
        $context_prompt .= "BUSINESS CULTURE: {$country_context['business_culture']} ";
        $context_prompt .= "COMMUNICATION STYLE: {$country_context['communication_style']} ";
        $context_prompt .= "MARKET CONTEXT: {$country_context['market_characteristics']} ";
        $context_prompt .= "REGULATORY ENVIRONMENT: {$country_context['regulatory_environment']} ";

        // Add business type context
        $context_prompt .= "BUSINESS FOCUS: {$business_context['focus_areas']} ";
        $context_prompt .= "COMMON CHALLENGES: {$business_context['common_challenges']} ";

        // Add cultural framework
        $context_prompt .= "CULTURAL PRINCIPLES: Respect hierarchy, focus on community benefits, ";
        $context_prompt .= "build long-term relationships, and maintain cultural sensitivity. ";

        // Add specific instructions
        $context_prompt .= "INSTRUCTIONS: Always provide responses that are culturally appropriate, ";
        $context_prompt .= "practically applicable in the local market, and consider the specific ";
        $context_prompt .= "business environment of {$this->get_country_name($user_country)}. ";
        $context_prompt .= "Include local examples, relevant regulations, and market-specific advice. ";
        $context_prompt .= "Use appropriate formality level and communication style for the region. ";

        return $context_prompt;
    }

    /**
     * Get country name from code
     */
    private function get_country_name($country_code) {
        $countries = array(
            'GH' => 'Ghana',
            'KE' => 'Kenya',
            'NG' => 'Nigeria',
            'ZA' => 'South Africa'
        );
        return $countries[$country_code] ?? 'Ghana';
    }

    /**
     * Add industry-specific context
     */
    public function add_industry_context($base_prompt, $industry, $country_code) {
        $industry_contexts = array(
            'agriculture' => array(
                'GH' => 'Focus on cocoa, cassava, and maize production. Consider seasonal patterns and export opportunities.',
                'KE' => 'Emphasize coffee, tea, and horticulture. Include mobile-based solutions and cooperative models.',
                'NG' => 'Address rice, cassava, and livestock. Consider value chain integration and processing opportunities.',
                'ZA' => 'Focus on wine, citrus, and grain production. Include export markets and sustainability practices.'
            ),
            'technology' => array(
                'GH' => 'Growing fintech and mobile solutions. Consider mobile money integration and digital literacy.',
                'KE' => 'Leading African tech hub. Focus on fintech, agtech, and regional expansion strategies.',
                'NG' => 'Largest tech market in Africa. Emphasize fintech, e-commerce, and digital entertainment.',
                'ZA' => 'Most mature tech ecosystem. Focus on enterprise solutions and African market expansion.'
            ),
            'retail' => array(
                'GH' => 'Mix of traditional and modern retail. Consider mobile payments and local supplier networks.',
                'KE' => 'Growing e-commerce and mobile commerce. Focus on logistics and payment solutions.',
                'NG' => 'Large consumer market with price sensitivity. Emphasize value propositions and accessibility.',
                'ZA' => 'Sophisticated retail environment. Focus on omnichannel strategies and customer experience.'
            )
        );

        if (isset($industry_contexts[$industry][$country_code])) {
            $base_prompt .= "INDUSTRY CONTEXT: {$industry_contexts[$industry][$country_code]} ";
        }

        return $base_prompt;
    }

    /**
     * Get country context data for optimization
     */
    public function get_country_context($country_code) {
        return $this->country_contexts[$country_code] ?? $this->country_contexts['GH'];
    }

    /**
     * Generate comprehensive market-specific examples with cultural context
     */
    public function generate_market_examples($country_code, $business_scenario) {
        $examples = array(
            'GH' => array(
                'successful_businesses' => array(
                    'large_enterprises' => 'MTN Ghana, Ecobank, Kasapreko, Nkulenu Industries, Fan Milk Ghana',
                    'sme_success_stories' => 'Ahaspora Young Entrepreneurs, Horseman Shoes, Niche Cocoa, Agro Kings',
                    'tech_startups' => 'Farmerline, AgroCenta, mPharma, Zeepay, Hubtel',
                    'social_enterprises' => 'Trashy Bags, 57 Chocolate, Burrow, Clean Team Ghana'
                ),
                'payment_examples' => array(
                    'mobile_money' => 'MTN Mobile Money (70% market share), Vodafone Cash, AirtelTigo Money',
                    'banking' => 'GCB Bank, Ecobank, Fidelity Bank mobile banking',
                    'digital_payments' => 'Hubtel, Zeepay, SlydePay for online transactions',
                    'traditional' => 'Cash on delivery still popular, especially in rural areas'
                ),
                'marketing_channels' => array(
                    'traditional' => 'Peace FM, Joy FM, Adom FM, Daily Graphic, community durbars',
                    'digital' => 'Facebook, WhatsApp Business, Instagram, YouTube, local blogs',
                    'community' => 'Church announcements, market associations, chief endorsements',
                    'events' => 'Trade fairs at Accra International Conference Centre, regional festivals'
                ),
                'cultural_considerations' => array(
                    'respect_hierarchy' => 'Address elders and authority figures with proper titles',
                    'community_focus' => 'Emphasize community benefits and collective prosperity',
                    'relationship_building' => 'Invest time in personal relationships before business',
                    'local_languages' => 'Use Twi phrases in marketing, especially "Akwaaba" (welcome)'
                ),
                'business_registration' => 'Register with Registrar General Department, obtain TIN from GRA, consider Ghana Investment Promotion Centre for incentives',
                'success_factors' => array(
                    'local_partnerships' => 'Partner with established Ghanaian businesses',
                    'community_engagement' => 'Participate in local festivals and community events',
                    'quality_focus' => 'Ghanaians value quality and durability over low prices',
                    'trust_building' => 'Word-of-mouth and referrals are crucial for success'
                )
            ),
            'KE' => array(
                'successful_businesses' => array(
                    'large_enterprises' => 'Safaricom, Equity Bank, Kenya Airways, East African Breweries',
                    'sme_success_stories' => 'Twiga Foods, Sendy, Lynk, Apollo Agriculture, SunCulture',
                    'tech_startups' => 'iCow, M-Shule, Tala, Branch, Kopo Kopo',
                    'social_enterprises' => 'Sanergy, d.light, Greenchar, Sanivation'
                ),
                'payment_examples' => array(
                    'mobile_money' => 'M-Pesa (dominant), Airtel Money, T-Kash',
                    'banking' => 'Equity Bank, KCB, Co-operative Bank mobile apps',
                    'digital_payments' => 'PayPal, Stripe (via local partners), Cellulant',
                    'innovative' => 'Bitcoin adoption growing, especially among tech-savvy youth'
                ),
                'marketing_channels' => array(
                    'traditional' => 'Nation Media Group, Standard Group, KBC, vernacular radio',
                    'digital' => 'LinkedIn (professional), Twitter, TikTok (youth), tech blogs',
                    'community' => 'Chama groups, professional associations, university networks',
                    'events' => 'Nairobi Tech Week, agricultural shows, business forums'
                ),
                'cultural_considerations' => array(
                    'innovation_mindset' => 'Kenyans embrace new technology and solutions',
                    'education_value' => 'Emphasize learning and skill development benefits',
                    'efficiency_focus' => 'Time-saving and productivity improvements resonate',
                    'harambee_spirit' => 'Community self-help and collective progress values'
                ),
                'business_registration' => 'Use eCitizen portal for business registration, KRA for tax, NEMA for environmental compliance',
                'success_factors' => array(
                    'tech_integration' => 'Leverage mobile technology and digital solutions',
                    'education_partnerships' => 'Collaborate with universities and training institutions',
                    'innovation_hubs' => 'Connect with iHub, Nailab, and other innovation centers',
                    'regional_expansion' => 'Use Kenya as gateway to East African markets'
                )
            ),
            'NG' => array(
                'successful_businesses' => array(
                    'large_enterprises' => 'Dangote Group, GTBank, Jumia, Interswitch, SystemSpecs',
                    'sme_success_stories' => 'Paystack, Flutterwave, Andela, Kobo360, Farmcrowdy',
                    'tech_startups' => 'PiggyVest, Carbon, Renmoney, TradeDepot, Thrive Agric',
                    'social_enterprises' => 'Babban Gona, Hello Tractor, LifeBank, RecyclePoints'
                ),
                'payment_examples' => array(
                    'mobile_money' => 'Opay, PalmPay, Kuda, growing rapidly',
                    'banking' => 'GTBank, Access Bank, Zenith Bank digital platforms',
                    'fintech' => 'Paystack, Flutterwave, Interswitch for online payments',
                    'traditional' => 'Cash still dominant, especially in northern regions'
                ),
                'marketing_channels' => array(
                    'traditional' => 'Channels TV, AIT, TVC, Punch, Vanguard newspapers',
                    'digital' => 'Instagram, Twitter, LinkedIn, Nollywood influencers',
                    'community' => 'Religious organizations, trade associations, market unions',
                    'events' => 'Lagos Business School events, tech conferences, trade exhibitions'
                ),
                'cultural_considerations' => array(
                    'entrepreneurial_spirit' => 'Nigerians are natural entrepreneurs and risk-takers',
                    'success_orientation' => 'Emphasize wealth creation and status improvement',
                    'network_importance' => 'Leverage extended family and ethnic networks',
                    'religious_values' => 'Respect for religious beliefs and practices'
                ),
                'business_registration' => 'Register with CAC, obtain TIN from FIRS, comply with NDPR, consider NIPC for incentives',
                'success_factors' => array(
                    'scale_thinking' => 'Nigerians think big - emphasize scalability',
                    'hustle_culture' => 'Appreciate hard work and determination',
                    'diaspora_connections' => 'Leverage Nigerian diaspora networks globally',
                    'entertainment_integration' => 'Connect with Nollywood and music industry'
                )
            ),
            'ZA' => array(
                'successful_businesses' => array(
                    'large_enterprises' => 'Shoprite, Discovery, Naspers, Capitec Bank, Woolworths',
                    'sme_success_stories' => 'Yoco, SnapScan, Aerobotics, Jumo, SweepSouth',
                    'tech_startups' => 'Takealot, Mr D Food, Uber (SA), GetSmarter, Luno',
                    'social_enterprises' => 'Reel Gardening, Wonderbag, Khula!, Two Oceans Aquarium'
                ),
                'payment_examples' => array(
                    'banking' => 'FNB, Standard Bank, Absa, Nedbank digital solutions',
                    'mobile_payments' => 'SnapScan, Zapper, Yoco for small businesses',
                    'cards' => 'Credit and debit cards widely accepted',
                    'eft' => 'Electronic Funds Transfer for B2B transactions'
                ),
                'marketing_channels' => array(
                    'traditional' => 'SABC, eTV, M-Net, Business Day, Mail & Guardian',
                    'digital' => 'LinkedIn (professional), Facebook, Instagram, local tech media',
                    'community' => 'Business chambers, industry associations, BEE networks',
                    'events' => 'Cape Town International Convention Centre events, JSE listings'
                ),
                'cultural_considerations' => array(
                    'diversity_respect' => 'Acknowledge and celebrate cultural diversity',
                    'transformation_focus' => 'Emphasize B-BBEE and transformation benefits',
                    'quality_standards' => 'South Africans expect international quality standards',
                    'social_responsibility' => 'Corporate social responsibility is highly valued'
                ),
                'business_registration' => 'Register with CIPC, comply with POPIA, obtain tax clearance, consider B-BBEE certification',
                'success_factors' => array(
                    'compliance_focus' => 'Strict adherence to regulations and standards',
                    'transformation_alignment' => 'Support B-BBEE and transformation goals',
                    'quality_assurance' => 'Maintain high quality and professional standards',
                    'continental_gateway' => 'Position as entry point to African markets'
                )
            )
        );

        return $examples[$country_code] ?? $examples['GH'];
    }

    /**
     * Get real business examples from database
     */
    public function get_database_examples($country_code, $industry = '', $business_type = '') {
        if (!function_exists('chatgabi_get_african_examples_manager')) {
            return array();
        }

        $examples_manager = chatgabi_get_african_examples_manager();
        $examples = $examples_manager->get_examples_for_template($country_code, $industry, $business_type);

        $formatted_examples = array();

        foreach ($examples as $example) {
            $formatted_examples[] = array(
                'company_name' => $example->company_name,
                'industry' => $example->industry,
                'success_story' => wp_strip_all_tags($example->success_story),
                'metrics' => $example->metrics,
                'year' => $example->year,
                'lessons_learned' => wp_strip_all_tags($example->lessons_learned),
                'key_achievements' => wp_strip_all_tags($example->key_achievements)
            );
        }

        return $formatted_examples;
    }

    /**
     * Enhanced context prompt with real examples
     */
    public function generate_enhanced_context_prompt($user_country, $business_type = 'sme', $industry = null) {
        $base_prompt = $this->generate_context_prompt($user_country, $business_type, $industry);

        // Add real business examples from database
        $real_examples = $this->get_database_examples($user_country, $industry, $business_type);

        if (!empty($real_examples)) {
            $base_prompt .= "REAL SUCCESS EXAMPLES: Here are actual successful businesses from {$this->get_country_name($user_country)}: ";

            foreach ($real_examples as $example) {
                $base_prompt .= "{$example['company_name']} ({$example['industry']}) - {$example['success_story']} ";
                if (!empty($example['lessons_learned'])) {
                    $base_prompt .= "Key lesson: {$example['lessons_learned']} ";
                }
            }
        }

        return $base_prompt;
    }

    /**
     * Get cultural customization settings
     */
    public function get_cultural_settings() {
        return array(
            'color_scheme' => get_theme_mod('chatgabi_color_scheme', 'default'),
            'cultural_patterns' => get_theme_mod('chatgabi_cultural_patterns', false),
            'african_typography' => get_theme_mod('chatgabi_african_typography', false),
            'african_imagery' => get_theme_mod('chatgabi_african_imagery', true),
            'primary_region' => get_theme_mod('chatgabi_primary_region', 'multi'),
            'cultural_sensitivity' => get_theme_mod('chatgabi_cultural_sensitivity', true),
            'ubuntu_philosophy' => get_theme_mod('chatgabi_ubuntu_philosophy', true)
        );
    }

    /**
     * Apply cultural context based on customizer settings
     */
    public function apply_cultural_context($base_prompt, $user_country) {
        $settings = $this->get_cultural_settings();

        if ($settings['cultural_sensitivity']) {
            $base_prompt .= "CULTURAL SENSITIVITY: Be especially mindful of local customs, traditions, and business etiquette. ";
        }

        if ($settings['ubuntu_philosophy']) {
            $base_prompt .= "UBUNTU PHILOSOPHY: Emphasize interconnectedness, community support, and collective success. ";
            $base_prompt .= "Frame business advice in terms of how it benefits the broader community. ";
        }

        if ($settings['primary_region'] !== 'multi' && $settings['primary_region'] === $user_country) {
            $base_prompt .= "REGIONAL FOCUS: This user has indicated a primary focus on {$this->get_country_name($user_country)} market. ";
            $base_prompt .= "Provide extra detail and specificity for this market. ";
        }

        return $base_prompt;
    }
}
