<?php
/**
 * Enhanced Template Creation Forms with AI Widgets
 * Comprehensive form system with embedded AI assistance
 * 
 * @package ChatGABI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate enhanced template creation form with AI widgets
 */
function chatgabi_generate_enhanced_template_form($template_type = 'business_plan', $user_context = array()) {
    $user_id = get_current_user_id();
    $user_country = $user_context['country'] ?? get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
    $user_industry = $user_context['industry'] ?? get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: 'general';
    $user_language = $user_context['language'] ?? get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
    
    $form_id = 'enhanced-template-form-' . uniqid();
    
    $html = '<div class="enhanced-template-form-container" data-template-type="' . esc_attr($template_type) . '">';
    $html .= '<form id="' . esc_attr($form_id) . '" class="enhanced-template-form">';
    
    // Form header with AI assistance indicator
    $html .= '<div class="form-header">';
    $html .= '<h3>' . sprintf(__('Create %s Template', 'chatgabi'), ucfirst(str_replace('_', ' ', $template_type))) . '</h3>';
    $html .= '<div class="ai-assistance-indicator">';
    $html .= '<span class="ai-icon">🤖</span>';
    $html .= '<span class="ai-text">' . __('AI-Powered Template Creation', 'chatgabi') . '</span>';
    $html .= '</div>';
    $html .= '</div>';
    
    // Business Information Section
    $html .= '<div class="form-section" data-section="business-info">';
    $html .= '<h4 class="section-title">' . __('Business Information', 'chatgabi') . '</h4>';
    
    // Business Name Field with AI
    $html .= '<div class="form-group">';
    $html .= '<label for="business-name">' . __('Business Name', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<input type="text" id="business-name" name="business_name" class="ai-enhanced-field" data-ai-type="business_name" required />';
    $html .= chatgabi_generate_ai_widget('business_name', array(
        'field_id' => 'business-name',
        'field_label' => 'Business Name'
    ), array(
        'country' => $user_country,
        'industry' => $user_industry,
        'language' => $user_language
    ));
    $html .= '</div>';
    $html .= '</div>';
    
    // Business Description Field with AI
    $html .= '<div class="form-group">';
    $html .= '<label for="business-description">' . __('Business Description', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="business-description" name="business_description" rows="4" class="ai-enhanced-field" data-ai-type="business_description" required></textarea>';
    $html .= chatgabi_generate_ai_widget('business_description', array(
        'field_id' => 'business-description',
        'field_label' => 'Business Description'
    ), array(
        'country' => $user_country,
        'industry' => $user_industry,
        'language' => $user_language
    ));
    $html .= '</div>';
    $html .= '<small class="form-help">' . __('Describe your business concept, products/services, and target market', 'chatgabi') . '</small>';
    $html .= '</div>';
    
    // Industry and Location
    $html .= '<div class="form-row">';
    $html .= '<div class="form-group">';
    $html .= '<label for="business-industry">' . __('Industry', 'chatgabi') . '</label>';
    $html .= '<select id="business-industry" name="business_industry" class="ai-enhanced-field" data-ai-type="industry">';
    $html .= '<option value="technology">' . __('Technology', 'chatgabi') . '</option>';
    $html .= '<option value="agriculture">' . __('Agriculture', 'chatgabi') . '</option>';
    $html .= '<option value="retail">' . __('Retail', 'chatgabi') . '</option>';
    $html .= '<option value="healthcare">' . __('Healthcare', 'chatgabi') . '</option>';
    $html .= '<option value="education">' . __('Education', 'chatgabi') . '</option>';
    $html .= '<option value="finance">' . __('Finance', 'chatgabi') . '</option>';
    $html .= '<option value="manufacturing">' . __('Manufacturing', 'chatgabi') . '</option>';
    $html .= '<option value="services">' . __('Services', 'chatgabi') . '</option>';
    $html .= '</select>';
    $html .= '</div>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="business-country">' . __('Primary Market', 'chatgabi') . '</label>';
    $html .= '<select id="business-country" name="business_country" class="ai-enhanced-field" data-ai-type="country">';
    $html .= '<option value="GH"' . selected($user_country, 'GH', false) . '>' . __('Ghana', 'chatgabi') . '</option>';
    $html .= '<option value="KE"' . selected($user_country, 'KE', false) . '>' . __('Kenya', 'chatgabi') . '</option>';
    $html .= '<option value="NG"' . selected($user_country, 'NG', false) . '>' . __('Nigeria', 'chatgabi') . '</option>';
    $html .= '<option value="ZA"' . selected($user_country, 'ZA', false) . '>' . __('South Africa', 'chatgabi') . '</option>';
    $html .= '</select>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>'; // End business-info section
    
    // Template-specific sections based on type
    switch ($template_type) {
        case 'business_plan':
            $html .= chatgabi_generate_business_plan_sections($user_context);
            break;
        case 'marketing_strategy':
            $html .= chatgabi_generate_marketing_strategy_sections($user_context);
            break;
        case 'financial_forecast':
            $html .= chatgabi_generate_financial_forecast_sections($user_context);
            break;
        default:
            $html .= chatgabi_generate_generic_template_sections($user_context);
    }
    
    // AI Enhancement Options
    $html .= '<div class="form-section ai-enhancement-section">';
    $html .= '<h4 class="section-title">' . __('AI Enhancement Options', 'chatgabi') . '</h4>';
    $html .= '<div class="enhancement-options-grid">';
    
    $html .= '<label class="enhancement-option">';
    $html .= '<input type="checkbox" name="ai_enhance_content" checked />';
    $html .= '<span class="option-icon">✨</span>';
    $html .= '<span class="option-text">' . __('Enhance content with AI', 'chatgabi') . '</span>';
    $html .= '<span class="option-cost">1 credit</span>';
    $html .= '</label>';
    
    $html .= '<label class="enhancement-option">';
    $html .= '<input type="checkbox" name="ai_add_local_context" checked />';
    $html .= '<span class="option-icon">🌍</span>';
    $html .= '<span class="option-text">' . __('Add local market context', 'chatgabi') . '</span>';
    $html .= '<span class="option-cost">1 credit</span>';
    $html .= '</label>';
    
    $html .= '<label class="enhancement-option">';
    $html .= '<input type="checkbox" name="ai_generate_examples" checked />';
    $html .= '<span class="option-icon">📊</span>';
    $html .= '<span class="option-text">' . __('Generate realistic examples', 'chatgabi') . '</span>';
    $html .= '<span class="option-cost">2 credits</span>';
    $html .= '</label>';
    
    $html .= '<label class="enhancement-option">';
    $html .= '<input type="checkbox" name="ai_cultural_adaptation" />';
    $html .= '<span class="option-icon">🎭</span>';
    $html .= '<span class="option-text">' . __('Cultural adaptation', 'chatgabi') . '</span>';
    $html .= '<span class="option-cost">1 credit</span>';
    $html .= '</label>';
    
    $html .= '</div>';
    $html .= '<div class="total-credits-display">';
    $html .= '<span class="credits-label">' . __('Total Credits:', 'chatgabi') . '</span>';
    $html .= '<span class="credits-amount" id="total-credits-amount">5</span>';
    $html .= '</div>';
    $html .= '</div>';
    
    // Form Actions
    $html .= '<div class="form-actions">';
    $html .= '<button type="button" class="btn-preview-template chatgabi-btn chatgabi-btn-secondary">';
    $html .= '👁️ ' . __('Preview Template', 'chatgabi');
    $html .= '</button>';
    $html .= '<button type="submit" class="btn-create-template chatgabi-btn chatgabi-btn-primary">';
    $html .= '🚀 ' . __('Create Template', 'chatgabi');
    $html .= '</button>';
    $html .= '</div>';
    
    $html .= '</form>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate business plan specific sections
 */
function chatgabi_generate_business_plan_sections($user_context) {
    $html = '';
    
    // Market Analysis Section
    $html .= '<div class="form-section" data-section="market-analysis">';
    $html .= '<h4 class="section-title">' . __('Market Analysis', 'chatgabi') . '</h4>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="target-market">' . __('Target Market', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="target-market" name="target_market" rows="3" class="ai-enhanced-field" data-ai-type="market_analysis"></textarea>';
    $html .= chatgabi_generate_ai_widget('market_analysis', array(
        'field_id' => 'target-market',
        'field_label' => 'Target Market'
    ), $user_context);
    $html .= '</div>';
    $html .= '<small class="form-help">' . __('Describe your ideal customers and market segments', 'chatgabi') . '</small>';
    $html .= '</div>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="competitive-analysis">' . __('Competitive Landscape', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="competitive-analysis" name="competitive_analysis" rows="3" class="ai-enhanced-field" data-ai-type="competitor_analysis"></textarea>';
    $html .= chatgabi_generate_ai_widget('market_analysis', array(
        'field_id' => 'competitive-analysis',
        'field_label' => 'Competitive Analysis'
    ), $user_context);
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    // Financial Projections Section
    $html .= '<div class="form-section" data-section="financial-projections">';
    $html .= '<h4 class="section-title">' . __('Financial Projections', 'chatgabi') . '</h4>';
    
    $html .= '<div class="form-row">';
    $html .= '<div class="form-group">';
    $html .= '<label for="startup-costs">' . __('Startup Costs', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<input type="text" id="startup-costs" name="startup_costs" class="ai-enhanced-field" data-ai-type="financial_projections" />';
    $html .= chatgabi_generate_ai_widget('financial_projections', array(
        'field_id' => 'startup-costs',
        'field_label' => 'Startup Costs'
    ), $user_context);
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="revenue-projections">' . __('Revenue Projections (Year 1)', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<input type="text" id="revenue-projections" name="revenue_projections" class="ai-enhanced-field" data-ai-type="financial_projections" />';
    $html .= chatgabi_generate_ai_widget('financial_projections', array(
        'field_id' => 'revenue-projections',
        'field_label' => 'Revenue Projections'
    ), $user_context);
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate marketing strategy specific sections
 */
function chatgabi_generate_marketing_strategy_sections($user_context) {
    $html = '';
    
    // Marketing Channels Section
    $html .= '<div class="form-section" data-section="marketing-channels">';
    $html .= '<h4 class="section-title">' . __('Marketing Channels', 'chatgabi') . '</h4>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="digital-marketing">' . __('Digital Marketing Strategy', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="digital-marketing" name="digital_marketing" rows="4" class="ai-enhanced-field" data-ai-type="marketing_strategy"></textarea>';
    $html .= chatgabi_generate_ai_widget('marketing_strategy', array(
        'field_id' => 'digital-marketing',
        'field_label' => 'Digital Marketing Strategy'
    ), $user_context);
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="traditional-marketing">' . __('Traditional Marketing Approach', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="traditional-marketing" name="traditional_marketing" rows="3" class="ai-enhanced-field" data-ai-type="marketing_strategy"></textarea>';
    $html .= chatgabi_generate_ai_widget('marketing_strategy', array(
        'field_id' => 'traditional-marketing',
        'field_label' => 'Traditional Marketing'
    ), $user_context);
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate financial forecast specific sections
 */
function chatgabi_generate_financial_forecast_sections($user_context) {
    $html = '';
    
    // Revenue Streams Section
    $html .= '<div class="form-section" data-section="revenue-streams">';
    $html .= '<h4 class="section-title">' . __('Revenue Streams', 'chatgabi') . '</h4>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="primary-revenue">' . __('Primary Revenue Sources', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="primary-revenue" name="primary_revenue" rows="3" class="ai-enhanced-field" data-ai-type="financial_projections"></textarea>';
    $html .= chatgabi_generate_ai_widget('financial_projections', array(
        'field_id' => 'primary-revenue',
        'field_label' => 'Primary Revenue Sources'
    ), $user_context);
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate generic template sections
 */
function chatgabi_generate_generic_template_sections($user_context) {
    $html = '';
    
    // Content Section
    $html .= '<div class="form-section" data-section="content">';
    $html .= '<h4 class="section-title">' . __('Template Content', 'chatgabi') . '</h4>';
    
    $html .= '<div class="form-group">';
    $html .= '<label for="template-content">' . __('Main Content', 'chatgabi') . '</label>';
    $html .= '<div class="form-field-with-ai">';
    $html .= '<textarea id="template-content" name="template_content" rows="8" class="ai-enhanced-field" data-ai-type="template_content"></textarea>';
    $html .= chatgabi_generate_ai_widget('template_content', array(
        'field_id' => 'template-content',
        'field_label' => 'Template Content'
    ), $user_context);
    $html .= '</div>';
    $html .= '<small class="form-help">' . __('Use {placeholders} for dynamic content that will be customized for each user', 'chatgabi') . '</small>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}
