<?php
/**
 * ChatGABI Multi-Language Verification Script
 * 
 * This script verifies that Priority 1 implementation is working correctly
 * Run this script to check all multi-language components
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

/**
 * Verification results
 */
function chatgabi_verify_multilanguage() {
    $results = array();
    
    // 1. Check text domain loading
    $results['textdomain_loaded'] = is_textdomain_loaded('chatgabi');
    
    // 2. Check language files exist
    $languages_dir = get_template_directory() . '/languages';
    $results['languages_dir_exists'] = is_dir($languages_dir);
    
    // 3. Check POT file
    $pot_file = $languages_dir . '/chatgabi.pot';
    $results['pot_file_exists'] = file_exists($pot_file);
    
    // 4. Check PO files
    $po_files = glob($languages_dir . '/*.po');
    $results['po_files_count'] = count($po_files);
    $results['po_files'] = array_map('basename', $po_files);
    
    // 5. Check MO files
    $mo_files = glob($languages_dir . '/*.mo');
    $results['mo_files_count'] = count($mo_files);
    $results['mo_files'] = array_map('basename', $mo_files);
    
    // 6. Test translation strings
    $test_strings = array(
        'Loading...',
        'ChatGABI AI Assistant',
        'Business Templates',
        'Send Message',
        'Language:'
    );
    
    $results['translation_tests'] = array();
    foreach ($test_strings as $string) {
        $translated = __($string, 'chatgabi');
        $results['translation_tests'][$string] = array(
            'original' => $string,
            'translated' => $translated,
            'is_different' => ($string !== $translated)
        );
    }
    
    // 7. Check function names consistency
    $results['functions_exist'] = array(
        'chatgabi_load_textdomain' => function_exists('chatgabi_load_textdomain'),
        'chatgabi_render_chat_block' => function_exists('chatgabi_render_chat_block'),
        'chatgabi_dashboard_shortcode' => function_exists('chatgabi_dashboard_shortcode'),
        'chatgabi_opportunities_shortcode' => function_exists('chatgabi_opportunities_shortcode')
    );
    
    // 8. Check WordPress locale
    $results['wp_locale'] = get_locale();
    
    // 9. Check theme directory
    $results['theme_directory'] = get_template_directory();
    
    // 10. Overall status
    $results['overall_status'] = (
        $results['textdomain_loaded'] &&
        $results['languages_dir_exists'] &&
        $results['pot_file_exists'] &&
        $results['po_files_count'] >= 4 &&
        $results['mo_files_count'] >= 3
    );
    
    return $results;
}

// Run verification
$verification = chatgabi_verify_multilanguage();

// Output results
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Multi-Language Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .status-pass { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #333; }
        table { width: 100%; border-collapse: collapse; margin: 1rem 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .overall-status { font-size: 1.5rem; text-align: center; padding: 2rem; margin: 2rem 0; border-radius: 8px; }
        .overall-pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .overall-fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ChatGABI Multi-Language Verification Report</h1>
        <p><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="overall-status <?php echo $verification['overall_status'] ? 'overall-pass' : 'overall-fail'; ?>">
            <h2>Overall Status: <?php echo $verification['overall_status'] ? '✅ PASS' : '❌ FAIL'; ?></h2>
            <p><?php echo $verification['overall_status'] ? 'Priority 1 implementation is working correctly!' : 'Some issues detected. Please review the details below.'; ?></p>
        </div>
        
        <div class="section">
            <h3>1. Text Domain Loading</h3>
            <table>
                <tr>
                    <td>Text Domain 'chatgabi' Loaded</td>
                    <td class="<?php echo $verification['textdomain_loaded'] ? 'status-pass' : 'status-fail'; ?>">
                        <?php echo $verification['textdomain_loaded'] ? '✅ YES' : '❌ NO'; ?>
                    </td>
                </tr>
                <tr>
                    <td>WordPress Locale</td>
                    <td><?php echo esc_html($verification['wp_locale']); ?></td>
                </tr>
                <tr>
                    <td>Theme Directory</td>
                    <td><?php echo esc_html($verification['theme_directory']); ?></td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h3>2. Language Files Structure</h3>
            <table>
                <tr>
                    <td>Languages Directory Exists</td>
                    <td class="<?php echo $verification['languages_dir_exists'] ? 'status-pass' : 'status-fail'; ?>">
                        <?php echo $verification['languages_dir_exists'] ? '✅ YES' : '❌ NO'; ?>
                    </td>
                </tr>
                <tr>
                    <td>POT File Exists</td>
                    <td class="<?php echo $verification['pot_file_exists'] ? 'status-pass' : 'status-fail'; ?>">
                        <?php echo $verification['pot_file_exists'] ? '✅ YES' : '❌ NO'; ?>
                    </td>
                </tr>
                <tr>
                    <td>PO Files Count</td>
                    <td class="<?php echo $verification['po_files_count'] >= 4 ? 'status-pass' : 'status-warning'; ?>">
                        <?php echo $verification['po_files_count']; ?> files
                    </td>
                </tr>
                <tr>
                    <td>MO Files Count</td>
                    <td class="<?php echo $verification['mo_files_count'] >= 3 ? 'status-pass' : 'status-warning'; ?>">
                        <?php echo $verification['mo_files_count']; ?> files
                    </td>
                </tr>
            </table>
            
            <h4>Available Language Files:</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div>
                    <strong>PO Files:</strong>
                    <ul>
                        <?php foreach ($verification['po_files'] as $file): ?>
                            <li><?php echo esc_html($file); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div>
                    <strong>MO Files:</strong>
                    <ul>
                        <?php foreach ($verification['mo_files'] as $file): ?>
                            <li><?php echo esc_html($file); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>3. Translation String Tests</h3>
            <table>
                <thead>
                    <tr>
                        <th>Original String</th>
                        <th>Translated String</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($verification['translation_tests'] as $test): ?>
                        <tr>
                            <td><?php echo esc_html($test['original']); ?></td>
                            <td><?php echo esc_html($test['translated']); ?></td>
                            <td class="<?php echo $test['is_different'] ? 'status-pass' : 'status-warning'; ?>">
                                <?php echo $test['is_different'] ? '✅ Translated' : '⚠️ Same as original'; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h3>4. Function Consistency Check</h3>
            <table>
                <?php foreach ($verification['functions_exist'] as $function => $exists): ?>
                    <tr>
                        <td><?php echo esc_html($function); ?>()</td>
                        <td class="<?php echo $exists ? 'status-pass' : 'status-fail'; ?>">
                            <?php echo $exists ? '✅ EXISTS' : '❌ MISSING'; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="section">
            <h3>5. Implementation Summary</h3>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                <h4>✅ Completed (Priority 1):</h4>
                <ul>
                    <li>Text domain standardization to 'chatgabi'</li>
                    <li>Text domain loading implementation</li>
                    <li>Language files structure creation</li>
                    <li>POT file generation with 105+ strings</li>
                    <li>African language translations (5 languages)</li>
                    <li>MO file generation system</li>
                </ul>
                
                <h4>🔄 Next Steps (Priority 2):</h4>
                <ul>
                    <li>JavaScript internationalization (wp.i18n)</li>
                    <li>Currency formatting for African markets</li>
                    <li>Language switching optimization</li>
                    <li>Template content translation</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h3>6. Testing Instructions</h3>
            <ol>
                <li><strong>Access Test Page:</strong> <a href="/test-multilanguage.php" target="_blank">/test-multilanguage.php</a></li>
                <li><strong>Check WordPress Admin:</strong> Verify no errors in admin dashboard</li>
                <li><strong>Test Chat Interface:</strong> Confirm all strings are translatable</li>
                <li><strong>Language Switching:</strong> Test language selection functionality</li>
                <li><strong>Template System:</strong> Verify template interface translations</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 2rem 0; padding: 1rem; background: #e7f3ff; border-radius: 8px;">
            <p><strong>ChatGABI Multi-Language Support - Priority 1 Implementation</strong></p>
            <p>Status: <?php echo $verification['overall_status'] ? '✅ COMPLETE AND FUNCTIONAL' : '❌ NEEDS ATTENTION'; ?></p>
        </div>
    </div>
</body>
</html>
