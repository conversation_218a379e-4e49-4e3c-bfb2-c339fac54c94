<?php
/**
 * Enhanced Language Switching System for ChatGABI
 * 
 * This file provides optimized language switching functionality
 * with AJAX support and real-time UI updates
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get supported languages with metadata
 * 
 * @return array Supported languages configuration
 */
function chatgabi_get_supported_languages() {
    return array(
        'en' => array(
            'name' => __('English', 'chatgabi'),
            'native_name' => 'English',
            'flag' => '🇺🇸',
            'locale' => 'en_US',
            'direction' => 'ltr',
            'countries' => array('US', 'GB', 'AU', 'CA'),
            'default_country' => 'US'
        ),
        'sw' => array(
            'name' => __('Swahili', 'chatgabi'),
            'native_name' => 'Kiswahili',
            'flag' => '🇰🇪',
            'locale' => 'sw_KE',
            'direction' => 'ltr',
            'countries' => array('KE', 'TZ', 'UG'),
            'default_country' => 'KE'
        ),
        'yo' => array(
            'name' => __('Yoruba', 'chatgabi'),
            'native_name' => 'Yorùbá',
            'flag' => '🇳🇬',
            'locale' => 'yo_NG',
            'direction' => 'ltr',
            'countries' => array('NG', 'BJ'),
            'default_country' => 'NG'
        ),
        'tw' => array(
            'name' => __('Twi', 'chatgabi'),
            'native_name' => 'Twi',
            'flag' => '🇬🇭',
            'locale' => 'tw_GH',
            'direction' => 'ltr',
            'countries' => array('GH'),
            'default_country' => 'GH'
        ),
        'zu' => array(
            'name' => __('Zulu', 'chatgabi'),
            'native_name' => 'IsiZulu',
            'flag' => '🇿🇦',
            'locale' => 'zu_ZA',
            'direction' => 'ltr',
            'countries' => array('ZA'),
            'default_country' => 'ZA'
        )
    );
}

/**
 * Get user's preferred language
 * 
 * @param int $user_id User ID (optional)
 * @return string Language code
 */
function chatgabi_get_user_preferred_language($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    // Priority order: User preference > Country mapping > Browser > Default
    
    // 1. Check user preference
    if ($user_id) {
        $user_language = get_user_meta($user_id, 'chatgabi_preferred_language', true);
        if ($user_language && array_key_exists($user_language, chatgabi_get_supported_languages())) {
            return $user_language;
        }
    }
    
    // 2. Check session for non-logged-in users
    if (!$user_id && session_id()) {
        if (isset($_SESSION['chatgabi_language'])) {
            $session_language = $_SESSION['chatgabi_language'];
            if (array_key_exists($session_language, chatgabi_get_supported_languages())) {
                return $session_language;
            }
        }
    }
    
    // 3. Check country-based mapping
    $user_country = chatgabi_get_user_country();
    if ($user_country) {
        $country_languages = array(
            'GH' => 'tw', // Ghana -> Twi
            'KE' => 'sw', // Kenya -> Swahili
            'NG' => 'yo', // Nigeria -> Yoruba
            'ZA' => 'zu'  // South Africa -> Zulu
        );
        
        if (isset($country_languages[$user_country])) {
            return $country_languages[$user_country];
        }
    }
    
    // 4. Browser language detection
    $browser_language = chatgabi_detect_browser_language();
    if ($browser_language && array_key_exists($browser_language, chatgabi_get_supported_languages())) {
        return $browser_language;
    }
    
    // 5. Default to English
    return 'en';
}

/**
 * Detect browser language preference
 * 
 * @return string|null Language code or null if not detected
 */
function chatgabi_detect_browser_language() {
    if (!isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
        return null;
    }
    
    $languages = explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']);
    $supported = array_keys(chatgabi_get_supported_languages());
    
    foreach ($languages as $language) {
        $lang_code = strtolower(substr(trim($language), 0, 2));
        if (in_array($lang_code, $supported)) {
            return $lang_code;
        }
    }
    
    return null;
}

/**
 * Render language switcher HTML
 * 
 * @param array $args Configuration arguments
 * @return string HTML output
 */
function chatgabi_render_language_switcher($args = array()) {
    $defaults = array(
        'current_language' => chatgabi_get_user_preferred_language(),
        'show_flags' => true,
        'show_native_names' => true,
        'style' => 'dropdown', // dropdown, buttons, minimal
        'class' => 'chatgabi-language-switcher',
        'id' => 'chatgabi-language-switcher'
    );
    
    $args = wp_parse_args($args, $defaults);
    $supported_languages = chatgabi_get_supported_languages();
    
    ob_start();
    ?>
    <div class="<?php echo esc_attr($args['class']); ?>" id="<?php echo esc_attr($args['id']); ?>">
        <?php if ($args['style'] === 'dropdown'): ?>
            <select class="chatgabi-language-select" data-current="<?php echo esc_attr($args['current_language']); ?>">
                <?php foreach ($supported_languages as $code => $language): ?>
                    <option value="<?php echo esc_attr($code); ?>" 
                            <?php selected($args['current_language'], $code); ?>
                            data-flag="<?php echo esc_attr($language['flag']); ?>"
                            data-direction="<?php echo esc_attr($language['direction']); ?>">
                        <?php 
                        $display_text = '';
                        if ($args['show_flags']) {
                            $display_text .= $language['flag'] . ' ';
                        }
                        if ($args['show_native_names']) {
                            $display_text .= $language['native_name'];
                        } else {
                            $display_text .= $language['name'];
                        }
                        echo esc_html($display_text);
                        ?>
                    </option>
                <?php endforeach; ?>
            </select>
        <?php elseif ($args['style'] === 'buttons'): ?>
            <div class="chatgabi-language-buttons">
                <?php foreach ($supported_languages as $code => $language): ?>
                    <button type="button" 
                            class="chatgabi-language-btn <?php echo $args['current_language'] === $code ? 'active' : ''; ?>"
                            data-language="<?php echo esc_attr($code); ?>"
                            data-flag="<?php echo esc_attr($language['flag']); ?>"
                            data-direction="<?php echo esc_attr($language['direction']); ?>"
                            title="<?php echo esc_attr($language['name']); ?>">
                        <?php if ($args['show_flags']): ?>
                            <span class="flag"><?php echo esc_html($language['flag']); ?></span>
                        <?php endif; ?>
                        <span class="name">
                            <?php echo esc_html($args['show_native_names'] ? $language['native_name'] : $language['name']); ?>
                        </span>
                    </button>
                <?php endforeach; ?>
            </div>
        <?php else: // minimal style ?>
            <div class="chatgabi-language-minimal">
                <span class="current-language" data-language="<?php echo esc_attr($args['current_language']); ?>">
                    <?php 
                    $current = $supported_languages[$args['current_language']];
                    if ($args['show_flags']) {
                        echo esc_html($current['flag'] . ' ');
                    }
                    echo esc_html($args['show_native_names'] ? $current['native_name'] : $current['name']);
                    ?>
                </span>
                <div class="language-dropdown">
                    <?php foreach ($supported_languages as $code => $language): ?>
                        <?php if ($code !== $args['current_language']): ?>
                            <a href="#" class="language-option" data-language="<?php echo esc_attr($code); ?>">
                                <?php 
                                if ($args['show_flags']) {
                                    echo esc_html($language['flag'] . ' ');
                                }
                                echo esc_html($args['show_native_names'] ? $language['native_name'] : $language['name']);
                                ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        chatgabiInitLanguageSwitcher('<?php echo esc_js($args['id']); ?>');
    });
    </script>
    <?php
    return ob_get_clean();
}

/**
 * AJAX handler for language switching
 */
function chatgabi_ajax_switch_language() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_language_nonce')) {
        wp_send_json_error(__('Security check failed', 'chatgabi'));
    }
    
    $language = sanitize_text_field($_POST['language']);
    $supported_languages = array_keys(chatgabi_get_supported_languages());
    
    if (!in_array($language, $supported_languages)) {
        wp_send_json_error(__('Invalid language', 'chatgabi'));
    }
    
    // Save preference
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        update_user_meta($user_id, 'chatgabi_preferred_language', $language);
    } else {
        // Store in session for non-logged-in users
        if (!session_id()) {
            session_start();
        }
        $_SESSION['chatgabi_language'] = $language;
    }
    
    // Get language data for frontend
    $languages = chatgabi_get_supported_languages();
    $language_data = $languages[$language];
    
    // Get translated strings for immediate UI update
    $strings = chatgabi_get_language_strings($language);
    
    wp_send_json_success(array(
        'language' => $language,
        'language_data' => $language_data,
        'strings' => $strings,
        'message' => __('Language switched successfully', 'chatgabi')
    ));
}

add_action('wp_ajax_chatgabi_switch_language', 'chatgabi_ajax_switch_language');
add_action('wp_ajax_nopriv_chatgabi_switch_language', 'chatgabi_ajax_switch_language');

/**
 * Get language strings for JavaScript
 * 
 * @param string $language Language code
 * @return array Translated strings
 */
function chatgabi_get_language_strings($language) {
    // Switch to the requested language temporarily
    $original_locale = get_locale();
    $languages = chatgabi_get_supported_languages();
    
    if (isset($languages[$language])) {
        switch_to_locale($languages[$language]['locale']);
    }
    
    $strings = array(
        'loading' => __('Loading...', 'chatgabi'),
        'error' => __('An error occurred', 'chatgabi'),
        'success' => __('Success', 'chatgabi'),
        'send_message' => __('Send Message', 'chatgabi'),
        'language' => __('Language:', 'chatgabi'),
        'templates' => __('Templates:', 'chatgabi'),
        'business_templates' => __('Business Templates', 'chatgabi'),
        'credits' => __('Credits:', 'chatgabi'),
        'login' => __('Login', 'chatgabi'),
        'register' => __('Register', 'chatgabi'),
        'search_templates' => __('Search templates...', 'chatgabi'),
        'select_template' => __('Select a template...', 'chatgabi'),
        'insufficient_credits' => __('Insufficient credits', 'chatgabi'),
        'network_error' => __('Network error occurred', 'chatgabi'),
        'please_wait' => __('Please wait...', 'chatgabi'),
        'try_again' => __('Try again', 'chatgabi'),
        'cancel' => __('Cancel', 'chatgabi'),
        'confirm' => __('Confirm', 'chatgabi'),
        'save' => __('Save', 'chatgabi'),
        'edit' => __('Edit', 'chatgabi'),
        'delete' => __('Delete', 'chatgabi'),
        'view' => __('View', 'chatgabi')
    );
    
    // Restore original locale
    switch_to_locale($original_locale);
    
    return $strings;
}

/**
 * Enqueue language switcher assets
 */
function chatgabi_enqueue_language_switcher_assets() {
    wp_localize_script('chatgabi-main', 'chatgabiLanguage', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('chatgabi_language_nonce'),
        'currentLanguage' => chatgabi_get_user_preferred_language(),
        'supportedLanguages' => chatgabi_get_supported_languages(),
        'strings' => chatgabi_get_language_strings(chatgabi_get_user_preferred_language())
    ));
}

add_action('wp_enqueue_scripts', 'chatgabi_enqueue_language_switcher_assets');

/**
 * Add language switcher to specific locations
 */
function chatgabi_add_language_switcher_to_header() {
    if (is_front_page() || is_page(array('templates', 'dashboard', 'preferences'))) {
        echo '<div class="header-language-switcher">';
        echo chatgabi_render_language_switcher(array(
            'style' => 'minimal',
            'show_flags' => true,
            'show_native_names' => true
        ));
        echo '</div>';
    }
}

add_action('wp_head', 'chatgabi_add_language_switcher_to_header');

/**
 * Shortcode for language switcher
 */
function chatgabi_language_switcher_shortcode($atts) {
    $atts = shortcode_atts(array(
        'style' => 'dropdown',
        'show_flags' => 'true',
        'show_native_names' => 'true',
        'class' => 'chatgabi-language-switcher'
    ), $atts);
    
    // Convert string booleans to actual booleans
    $atts['show_flags'] = filter_var($atts['show_flags'], FILTER_VALIDATE_BOOLEAN);
    $atts['show_native_names'] = filter_var($atts['show_native_names'], FILTER_VALIDATE_BOOLEAN);
    
    return chatgabi_render_language_switcher($atts);
}

add_shortcode('chatgabi_language_switcher', 'chatgabi_language_switcher_shortcode');
?>
