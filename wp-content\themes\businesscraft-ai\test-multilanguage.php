<?php
/**
 * Test Multi-Language Implementation
 * 
 * This file tests the ChatGABI multi-language support implementation
 * Access via: /test-multilanguage.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

get_header();
?>

<div class="container" style="padding: 2rem 0;">
    <h1><?php _e('ChatGABI Multi-Language Test', 'chatgabi'); ?></h1>
    
    <div class="multilanguage-test-section">
        <h2><?php _e('Text Domain Loading Test', 'chatgabi'); ?></h2>
        
        <div class="test-results">
            <h3><?php _e('Current Language Settings', 'chatgabi'); ?></h3>
            <ul>
                <li><strong><?php _e('WordPress Locale', 'chatgabi'); ?>:</strong> <?php echo get_locale(); ?></li>
                <li><strong><?php _e('Text Domain Loaded', 'chatgabi'); ?>:</strong> 
                    <?php echo is_textdomain_loaded('chatgabi') ? __('Yes', 'chatgabi') : __('No', 'chatgabi'); ?>
                </li>
                <li><strong><?php _e('Languages Directory', 'chatgabi'); ?>:</strong> 
                    <?php echo get_template_directory() . '/languages'; ?>
                </li>
                <li><strong><?php _e('Available Language Files', 'chatgabi'); ?>:</strong>
                    <ul>
                        <?php
                        $languages_dir = get_template_directory() . '/languages';
                        $language_files = glob($languages_dir . '/*.mo');
                        if (!empty($language_files)) {
                            foreach ($language_files as $file) {
                                echo '<li>' . basename($file) . '</li>';
                            }
                        } else {
                            echo '<li>' . __('No MO files found', 'chatgabi') . '</li>';
                        }
                        ?>
                    </ul>
                </li>
            </ul>
        </div>
        
        <div class="translation-test">
            <h3><?php _e('Translation Test Strings', 'chatgabi'); ?></h3>
            <table style="border-collapse: collapse; width: 100%; margin: 1rem 0;">
                <thead>
                    <tr style="background: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">
                            <?php _e('English String', 'chatgabi'); ?>
                        </th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">
                            <?php _e('Translated String', 'chatgabi'); ?>
                        </th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">
                            <?php _e('Status', 'chatgabi'); ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $test_strings = array(
                        'Loading...',
                        'An error occurred',
                        'Thank you for your feedback!',
                        'ChatGABI AI Assistant',
                        'Business Templates',
                        'Language:',
                        'Templates:',
                        'Send Message',
                        'Login',
                        'Register'
                    );
                    
                    foreach ($test_strings as $string) {
                        $translated = __($string, 'chatgabi');
                        $is_translated = ($string !== $translated);
                        echo '<tr>';
                        echo '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($string) . '</td>';
                        echo '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($translated) . '</td>';
                        echo '<td style="border: 1px solid #ddd; padding: 8px; color: ' . ($is_translated ? 'green' : 'orange') . ';">';
                        echo $is_translated ? __('Translated', 'chatgabi') : __('Not Translated', 'chatgabi');
                        echo '</td>';
                        echo '</tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
        
        <div class="language-switching-test">
            <h3><?php _e('Language Switching Test', 'chatgabi'); ?></h3>
            <p><?php _e('Test language switching functionality:', 'chatgabi'); ?></p>
            
            <div class="language-buttons" style="margin: 1rem 0;">
                <button onclick="testLanguageSwitch('en')" style="margin: 0.5rem; padding: 0.5rem 1rem; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    <?php _e('English', 'chatgabi'); ?>
                </button>
                <button onclick="testLanguageSwitch('sw')" style="margin: 0.5rem; padding: 0.5rem 1rem; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    <?php _e('Swahili', 'chatgabi'); ?>
                </button>
                <button onclick="testLanguageSwitch('yo')" style="margin: 0.5rem; padding: 0.5rem 1rem; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    <?php _e('Yoruba', 'chatgabi'); ?>
                </button>
                <button onclick="testLanguageSwitch('tw')" style="margin: 0.5rem; padding: 0.5rem 1rem; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    <?php _e('Twi', 'chatgabi'); ?>
                </button>
                <button onclick="testLanguageSwitch('zu')" style="margin: 0.5rem; padding: 0.5rem 1rem; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    <?php _e('Zulu', 'chatgabi'); ?>
                </button>
            </div>
            
            <div id="language-test-result" style="margin: 1rem 0; padding: 1rem; background: #f9f9f9; border-radius: 4px;">
                <?php _e('Click a language button to test switching', 'chatgabi'); ?>
            </div>
        </div>
        
        <div class="african-context-test">
            <h3><?php _e('African Context Integration Test', 'chatgabi'); ?></h3>
            <p><?php _e('Testing African business terminology and cultural context:', 'chatgabi'); ?></p>
            
            <div class="context-examples">
                <h4><?php _e('Business Terms by Language', 'chatgabi'); ?></h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 1rem 0;">
                    <div style="background: #f5f5f5; padding: 1rem; border-radius: 4px;">
                        <h5><?php _e('English', 'chatgabi'); ?></h5>
                        <ul>
                            <li><?php _e('Entrepreneur', 'chatgabi'); ?></li>
                            <li><?php _e('Business Plan', 'chatgabi'); ?></li>
                            <li><?php _e('Market Research', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div style="background: #f5f5f5; padding: 1rem; border-radius: 4px;">
                        <h5><?php _e('Swahili', 'chatgabi'); ?></h5>
                        <ul>
                            <li>Mfanyabiashara</li>
                            <li>Mpango wa biashara</li>
                            <li>Utafiti wa soko</li>
                        </ul>
                    </div>
                    <div style="background: #f5f5f5; padding: 1rem; border-radius: 4px;">
                        <h5><?php _e('Yoruba', 'chatgabi'); ?></h5>
                        <ul>
                            <li>Onisowo</li>
                            <li>Eto iṣowo</li>
                            <li>Iwadi oja</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="implementation-status">
            <h3><?php _e('Implementation Status', 'chatgabi'); ?></h3>
            <div style="background: #e7f3ff; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
                <h4 style="color: #0073aa; margin-top: 0;"><?php _e('Priority 1 Completed ✅', 'chatgabi'); ?></h4>
                <ul>
                    <li>✅ <?php _e('Text domain standardized to "chatgabi"', 'chatgabi'); ?></li>
                    <li>✅ <?php _e('Text domain loading implemented', 'chatgabi'); ?></li>
                    <li>✅ <?php _e('Language files structure created', 'chatgabi'); ?></li>
                    <li>✅ <?php _e('POT file generated', 'chatgabi'); ?></li>
                    <li>✅ <?php _e('African language translations created', 'chatgabi'); ?></li>
                </ul>
            </div>
            
            <div style="background: #fff2e7; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
                <h4 style="color: #d63638; margin-top: 0;"><?php _e('Next Steps (Priority 2)', 'chatgabi'); ?></h4>
                <ul>
                    <li>🔄 <?php _e('JavaScript internationalization implementation', 'chatgabi'); ?></li>
                    <li>🔄 <?php _e('Currency formatting for African markets', 'chatgabi'); ?></li>
                    <li>🔄 <?php _e('Language switching optimization', 'chatgabi'); ?></li>
                    <li>🔄 <?php _e('Template content translation', 'chatgabi'); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function testLanguageSwitch(languageCode) {
    const resultDiv = document.getElementById('language-test-result');
    resultDiv.innerHTML = '<?php _e("Testing language switch to", "chatgabi"); ?> ' + languageCode + '...';
    
    // Simulate language switching (in real implementation, this would be an AJAX call)
    setTimeout(() => {
        const messages = {
            'en': '<?php _e("Language switched to English", "chatgabi"); ?>',
            'sw': 'Lugha imebadilishwa kuwa Kiswahili',
            'yo': 'Ede ti yi pada si Yoruba',
            'tw': 'Kasa no adan akɔ Twi',
            'zu': 'Ulimi luguqukele ku-IsiZulu'
        };
        
        resultDiv.innerHTML = messages[languageCode] || '<?php _e("Language switch test completed", "chatgabi"); ?>';
        resultDiv.style.background = '#d4edda';
        resultDiv.style.color = '#155724';
        resultDiv.style.border = '1px solid #c3e6cb';
    }, 1000);
}
</script>

<?php
get_footer();
?>
