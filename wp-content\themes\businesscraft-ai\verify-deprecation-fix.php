<?php
/**
 * Verify PHP Deprecation Warnings Fix for African Market Customization
 * Final verification that all null value issues are resolved
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Enable all error reporting to catch any warnings
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI - Deprecation Warnings Fix Verification</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; background: #f6f7f9; }
        .container { max-width: 900px; margin: 20px auto; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .success { color: #155724; background: #d4edda; border: 1px solid #c3e6cb; padding: 12px 16px; border-radius: 6px; margin: 12px 0; }
        .error { color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; padding: 12px 16px; border-radius: 6px; margin: 12px 0; }
        .warning { color: #856404; background: #fff3cd; border: 1px solid #ffeaa7; padding: 12px 16px; border-radius: 6px; margin: 12px 0; }
        .info { color: #0c5460; background: #d1ecf1; border: 1px solid #bee5eb; padding: 12px 16px; border-radius: 6px; margin: 12px 0; }
        .test-group { background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; border-left: 4px solid #007cba; }
        .test-item { display: flex; align-items: center; padding: 8px 0; border-bottom: 1px solid #e9ecef; }
        .test-item:last-child { border-bottom: none; }
        .test-status { width: 30px; margin-right: 12px; font-size: 18px; }
        .test-name { flex: 1; font-weight: 500; }
        .test-details { font-size: 14px; color: #6c757d; margin-left: 42px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #e9ecef; }
        .stat-number { font-size: 32px; font-weight: bold; color: #007cba; margin-bottom: 8px; }
        .stat-label { color: #6c757d; font-size: 14px; }
        .btn { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 6px; text-decoration: none; display: inline-block; font-weight: 500; transition: background 0.2s; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto; font-size: 13px; border: 1px solid #e9ecef; }
        .progress-bar { background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden; margin: 10px 0; }
        .progress-fill { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; transition: width 0.3s ease; }
    </style>
</head>
<body>
<div class='container'>
    <div class='header'>
        <h1>🔧 ChatGABI Deprecation Warnings Fix</h1>
        <p>Comprehensive verification of PHP null value handling improvements</p>
    </div>
    <div class='content'>";

// Initialize test tracking
$tests = array();
$warnings_detected = array();
$total_tests = 0;
$passed_tests = 0;

// Custom error handler to catch deprecation warnings
function deprecation_warning_handler($errno, $errstr, $errfile, $errline) {
    global $warnings_detected;
    
    if ($errno === E_DEPRECATED || 
        strpos($errstr, 'Passing null to parameter') !== false ||
        strpos($errstr, 'ltrim()') !== false) {
        
        $warnings_detected[] = array(
            'type' => 'deprecation',
            'message' => $errstr,
            'file' => basename($errfile),
            'line' => $errline,
            'context' => 'African Examples Manager'
        );
    }
    
    return false; // Don't suppress the error
}

set_error_handler('deprecation_warning_handler');

// Test 1: Load and Initialize Manager
echo "<div class='test-group'>";
echo "<h2>📦 Module Loading & Initialization</h2>";

$total_tests++;
try {
    require_once get_template_directory() . '/inc/african-examples-manager.php';
    
    if (function_exists('chatgabi_get_african_examples_manager')) {
        $examples_manager = chatgabi_get_african_examples_manager();
        
        if ($examples_manager instanceof ChatGABI_African_Examples_Manager) {
            $tests['manager_init'] = true;
            $passed_tests++;
            echo "<div class='test-item'><span class='test-status'>✅</span><span class='test-name'>African Examples Manager loaded and initialized</span></div>";
        } else {
            $tests['manager_init'] = false;
            echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Manager instance creation failed</span></div>";
        }
    } else {
        $tests['manager_init'] = false;
        echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Manager function not found</span></div>";
    }
} catch (Exception $e) {
    $tests['manager_init'] = false;
    echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Exception during initialization</span></div>";
    echo "<div class='test-details'>Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 2: Sample Data Processing
echo "<div class='test-group'>";
echo "<h2>📊 Sample Data Processing</h2>";

$total_tests++;
$warnings_before = count($warnings_detected);

try {
    if (isset($examples_manager)) {
        // Test sample data retrieval
        $reflection = new ReflectionClass($examples_manager);
        $get_sample_method = $reflection->getMethod('get_sample_examples');
        $get_sample_method->setAccessible(true);
        
        $sample_data = $get_sample_method->invoke($examples_manager);
        
        $warnings_after = count($warnings_detected);
        $new_warnings = $warnings_after - $warnings_before;
        
        if ($new_warnings === 0 && is_array($sample_data) && !empty($sample_data)) {
            $tests['sample_data'] = true;
            $passed_tests++;
            echo "<div class='test-item'><span class='test-status'>✅</span><span class='test-name'>Sample data processed without warnings</span></div>";
            echo "<div class='test-details'>Retrieved " . count($sample_data) . " business examples</div>";
            
            // Verify data completeness
            $first_example = $sample_data[0];
            $required_fields = array('company_name', 'country', 'industry', 'success_story', 'contact_info', 'website_url', 'logo_url');
            $missing_fields = array();
            
            foreach ($required_fields as $field) {
                if (!array_key_exists($field, $first_example)) {
                    $missing_fields[] = $field;
                }
            }
            
            if (empty($missing_fields)) {
                echo "<div class='test-item'><span class='test-status'>✅</span><span class='test-name'>All required fields present in sample data</span></div>";
            } else {
                echo "<div class='test-item'><span class='test-status'>⚠️</span><span class='test-name'>Some fields missing: " . implode(', ', $missing_fields) . "</span></div>";
            }
            
        } else {
            $tests['sample_data'] = false;
            echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Sample data processing failed or generated warnings</span></div>";
            if ($new_warnings > 0) {
                echo "<div class='test-details'>Generated {$new_warnings} new warnings</div>";
            }
        }
    } else {
        $tests['sample_data'] = false;
        echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Manager not available for testing</span></div>";
    }
} catch (Exception $e) {
    $tests['sample_data'] = false;
    echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Exception during sample data processing</span></div>";
    echo "<div class='test-details'>Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Null Value Handling
echo "<div class='test-group'>";
echo "<h2>🛡️ Null Value Handling</h2>";

$total_tests++;
$warnings_before = count($warnings_detected);

try {
    if (isset($examples_manager)) {
        // Test with intentionally incomplete data
        $test_data = array(
            'company_name' => 'Test Company',
            'country' => 'GH',
            'industry' => 'Technology',
            'success_story' => 'Test story',
            'year' => 2023,
            // Intentionally omitting: contact_info, website_url, logo_url, metrics, etc.
        );
        
        $result = $examples_manager->add_example($test_data);
        
        $warnings_after = count($warnings_detected);
        $new_warnings = $warnings_after - $warnings_before;
        
        if ($new_warnings === 0 && !is_wp_error($result)) {
            $tests['null_handling'] = true;
            $passed_tests++;
            echo "<div class='test-item'><span class='test-status'>✅</span><span class='test-name'>Null values handled without deprecation warnings</span></div>";
            echo "<div class='test-details'>Test example added successfully (ID: {$result})</div>";
            
            // Clean up test data
            $examples_manager->delete_example($result);
            echo "<div class='test-item'><span class='test-status'>🧹</span><span class='test-name'>Test data cleaned up</span></div>";
            
        } else {
            $tests['null_handling'] = false;
            echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Null handling still generates warnings</span></div>";
            if ($new_warnings > 0) {
                echo "<div class='test-details'>Generated {$new_warnings} new warnings</div>";
            }
            if (is_wp_error($result)) {
                echo "<div class='test-details'>WP Error: " . $result->get_error_message() . "</div>";
            }
        }
    } else {
        $tests['null_handling'] = false;
        echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Manager not available for testing</span></div>";
    }
} catch (Exception $e) {
    $tests['null_handling'] = false;
    echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Exception during null handling test</span></div>";
    echo "<div class='test-details'>Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 4: Safe Sanitization Methods
echo "<div class='test-group'>";
echo "<h2>🔒 Safe Sanitization Methods</h2>";

$total_tests++;
$sanitization_methods = array(
    'safe_sanitize_text_field',
    'safe_sanitize_textarea_field', 
    'safe_wp_kses_post',
    'safe_esc_url_raw'
);

$sanitization_passed = 0;
$warnings_before = count($warnings_detected);

try {
    if (isset($examples_manager)) {
        $reflection = new ReflectionClass($examples_manager);
        
        foreach ($sanitization_methods as $method_name) {
            try {
                $method = $reflection->getMethod($method_name);
                $method->setAccessible(true);
                
                // Test with null value
                $result = $method->invoke($examples_manager, null);
                
                if ($result === '') {
                    $sanitization_passed++;
                    echo "<div class='test-item'><span class='test-status'>✅</span><span class='test-name'>{$method_name}() handles null correctly</span></div>";
                } else {
                    echo "<div class='test-item'><span class='test-status'>⚠️</span><span class='test-name'>{$method_name}() returned unexpected result</span></div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>{$method_name}() threw exception</span></div>";
            }
        }
        
        $warnings_after = count($warnings_detected);
        $new_warnings = $warnings_after - $warnings_before;
        
        if ($sanitization_passed === count($sanitization_methods) && $new_warnings === 0) {
            $tests['sanitization'] = true;
            $passed_tests++;
            echo "<div class='test-item'><span class='test-status'>✅</span><span class='test-name'>All sanitization methods working correctly</span></div>";
        } else {
            $tests['sanitization'] = false;
            echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Some sanitization methods have issues</span></div>";
        }
    } else {
        $tests['sanitization'] = false;
        echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Manager not available for testing</span></div>";
    }
} catch (Exception $e) {
    $tests['sanitization'] = false;
    echo "<div class='test-item'><span class='test-status'>❌</span><span class='test-name'>Exception during sanitization testing</span></div>";
}

echo "</div>";

// Restore error handler
restore_error_handler();

// Results Summary
$success_rate = $total_tests > 0 ? ($passed_tests / $total_tests) * 100 : 0;

echo "<div class='stats-grid'>";
echo "<div class='stat-card'><div class='stat-number'>{$total_tests}</div><div class='stat-label'>Total Tests</div></div>";
echo "<div class='stat-card'><div class='stat-number'>{$passed_tests}</div><div class='stat-label'>Tests Passed</div></div>";
echo "<div class='stat-card'><div class='stat-number'>" . count($warnings_detected) . "</div><div class='stat-label'>Warnings Detected</div></div>";
echo "<div class='stat-card'><div class='stat-number'>" . round($success_rate, 1) . "%</div><div class='stat-label'>Success Rate</div></div>";
echo "</div>";

echo "<div class='progress-bar'>";
echo "<div class='progress-fill' style='width: {$success_rate}%'></div>";
echo "</div>";

// Final Result
if (count($warnings_detected) === 0 && $success_rate === 100) {
    echo "<div class='success'>";
    echo "<h3>🎉 All Deprecation Warnings Fixed Successfully!</h3>";
    echo "<p><strong>Verification Complete:</strong> The African Market Customization system now properly handles null values and prevents PHP deprecation warnings during initialization.</p>";
    echo "<ul>";
    echo "<li>✅ Safe sanitization methods implemented</li>";
    echo "<li>✅ Comprehensive data normalization in place</li>";
    echo "<li>✅ Null value handling throughout the system</li>";
    echo "<li>✅ Robust error handling and logging</li>";
    echo "</ul>";
    echo "</div>";
} else {
    if (count($warnings_detected) > 0) {
        echo "<div class='error'>";
        echo "<h3>⚠️ Deprecation Warnings Still Present</h3>";
        echo "<p>The following warnings were detected during testing:</p>";
        echo "<pre>";
        foreach ($warnings_detected as $warning) {
            echo "• {$warning['message']} in {$warning['file']}:{$warning['line']}\n";
        }
        echo "</pre>";
        echo "</div>";
    }
    
    if ($success_rate < 100) {
        echo "<div class='warning'>";
        echo "<h3>🔧 Some Tests Failed</h3>";
        echo "<p>Please review the failed tests above and address any remaining issues.</p>";
        echo "</div>";
    }
}

echo "<div style='text-align: center; margin: 30px 0;'>";
if (count($warnings_detected) === 0 && $success_rate === 100) {
    echo "<a href='initialize-african-market-customization.php' class='btn btn-success'>✅ Run Full Initialization</a>";
} else {
    echo "<a href='initialize-african-market-customization.php' class='btn'>🔧 Run Initialization (with caution)</a>";
}
echo "</div>";

echo "</div></div></body></html>";
?>
