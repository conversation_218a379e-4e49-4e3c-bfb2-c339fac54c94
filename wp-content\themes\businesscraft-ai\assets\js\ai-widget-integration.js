/**
 * AI Widget Integration JavaScript
 * Handles real-time AI assistance for template forms
 * 
 * @package ChatGABI
 * @since 2.0.0
 */

(function($) {
    'use strict';

    // AI Widget Manager
    const AIWidgetManager = {
        init: function() {
            this.bindEvents();
            this.initializeWidgets();
            this.setupRealTimeSuggestions();
        },

        bindEvents: function() {
            // AI enhancement button clicks
            $(document).on('click', '.ai-enhance-btn', this.handleEnhancementRequest.bind(this));
            
            // AI widget toggle
            $(document).on('click', '.ai-widget-toggle', this.toggleWidget.bind(this));
            
            // Suggestion item clicks
            $(document).on('click', '.suggestion-item', this.applySuggestion.bind(this));
            
            // Real-time typing suggestions
            $(document).on('input', '.ai-enhanced-field', this.handleRealTimeInput.bind(this));
            
            // Field focus events for context-aware suggestions
            $(document).on('focus', '.ai-enhanced-field', this.loadFieldSuggestions.bind(this));
        },

        initializeWidgets: function() {
            $('.chatgabi-ai-widget').each(function() {
                const $widget = $(this);
                const fieldType = $widget.data('field-type');
                
                // Initialize widget state
                $widget.addClass('ai-widget-initialized');
                
                // Load initial suggestions
                AIWidgetManager.loadInitialSuggestions($widget, fieldType);
            });
        },

        setupRealTimeSuggestions: function() {
            // Debounced input handler for real-time suggestions
            this.realTimeDebounce = {};
        },

        handleEnhancementRequest: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const action = $button.data('action');
            const fieldId = $button.data('field');
            const $field = $('#' + fieldId);
            const currentContent = $field.val();
            
            if (!currentContent.trim()) {
                this.showMessage(__('Please enter some content before requesting AI enhancement.', 'chatgabi'), 'warning');
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).html('<span class="spinner"></span> ' + __('Enhancing...', 'chatgabi'));
            
            // Get user context
            const userContext = this.getUserContext();
            
            // Make API request
            $.ajax({
                url: chatgabiConfig.restUrl + 'ai-widgets/enhance',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': chatgabiConfig.restNonce
                },
                data: {
                    field_id: fieldId,
                    action: action,
                    current_content: currentContent,
                    user_context: userContext
                },
                success: function(response) {
                    if (response.success) {
                        AIWidgetManager.handleEnhancementSuccess(response, $field, $button);
                    } else {
                        AIWidgetManager.showMessage(__('Enhancement failed. Please try again.', 'chatgabi'), 'error');
                    }
                },
                error: function(xhr) {
                    const errorMessage = xhr.responseJSON?.message || __('Enhancement failed. Please try again.', 'chatgabi');
                    AIWidgetManager.showMessage(errorMessage, 'error');
                },
                complete: function() {
                    // Restore button state
                    $button.prop('disabled', false).html('✨ ' + __('Enhance Content', 'chatgabi'));
                }
            });
        },

        handleEnhancementSuccess: function(response, $field, $button) {
            const $widget = $button.closest('.chatgabi-ai-widget');
            const $suggestionsDisplay = $widget.find('.ai-suggestions-display');
            
            // Show enhanced content in a modal or inline
            const enhancementHtml = `
                <div class="enhancement-result">
                    <h5>${__('AI Enhanced Content', 'chatgabi')}</h5>
                    <div class="enhanced-content">${response.enhanced_content}</div>
                    <div class="enhancement-actions">
                        <button class="btn-apply-enhancement chatgabi-btn chatgabi-btn-primary" data-content="${response.enhanced_content}">
                            ${__('Apply Enhancement', 'chatgabi')}
                        </button>
                        <button class="btn-merge-enhancement chatgabi-btn chatgabi-btn-secondary" data-original="${response.original_content}" data-enhanced="${response.enhanced_content}">
                            ${__('Merge with Original', 'chatgabi')}
                        </button>
                    </div>
                    <div class="enhancement-info">
                        <span class="credits-used">${__('Credits used:', 'chatgabi')} ${response.credits_used}</span>
                        <span class="remaining-credits">${__('Remaining:', 'chatgabi')} ${response.remaining_credits}</span>
                    </div>
                </div>
            `;
            
            $suggestionsDisplay.html(enhancementHtml);
            
            // Update user credits display
            this.updateCreditsDisplay(response.remaining_credits);
            
            // Show success message
            this.showMessage(__('Content enhanced successfully!', 'chatgabi'), 'success');
        },

        toggleWidget: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const widgetId = $button.closest('.chatgabi-ai-widget').data('widget-id');
            const $content = $('#' + widgetId + '-content');
            const $icon = $button.find('.toggle-icon');
            
            if ($content.is(':visible')) {
                $content.slideUp();
                $icon.text('▼');
            } else {
                $content.slideDown();
                $icon.text('▲');
            }
        },

        applySuggestion: function(e) {
            e.preventDefault();
            
            const $item = $(e.currentTarget);
            const suggestionText = $item.find('.suggestion-text').text();
            const $widget = $item.closest('.chatgabi-ai-widget');
            const fieldType = $widget.data('field-type');
            
            // Find the associated field
            const $field = $widget.siblings('input, textarea').first();
            
            if ($field.length) {
                // Apply suggestion based on field type
                if ($field.is('textarea')) {
                    const currentValue = $field.val();
                    const newValue = currentValue ? currentValue + '\n\n' + suggestionText : suggestionText;
                    $field.val(newValue);
                } else {
                    $field.val(suggestionText);
                }
                
                // Trigger change event
                $field.trigger('change');
                
                // Show feedback
                $item.addClass('suggestion-applied');
                setTimeout(() => $item.removeClass('suggestion-applied'), 2000);
            }
        },

        handleRealTimeInput: function(e) {
            const $field = $(e.currentTarget);
            const fieldType = $field.data('ai-type');
            const content = $field.val();
            
            // Clear previous timeout
            if (this.realTimeDebounce[fieldType]) {
                clearTimeout(this.realTimeDebounce[fieldType]);
            }
            
            // Set new timeout for debounced suggestions
            this.realTimeDebounce[fieldType] = setTimeout(() => {
                this.getRealTimeSuggestions(fieldType, content, $field);
            }, 1000); // 1 second delay
        },

        getRealTimeSuggestions: function(fieldType, content, $field) {
            if (content.length < 10) return; // Too short for meaningful suggestions
            
            const userContext = this.getUserContext();
            
            $.ajax({
                url: chatgabiConfig.restUrl + 'ai-widgets/real-time-suggestions',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': chatgabiConfig.restNonce
                },
                data: {
                    field_type: fieldType,
                    partial_content: content,
                    user_context: userContext
                },
                success: function(response) {
                    if (response.success && response.suggestions.length > 0) {
                        AIWidgetManager.displayRealTimeSuggestions(response.suggestions, $field);
                    }
                },
                error: function(xhr) {
                    console.log('Real-time suggestions error:', xhr);
                }
            });
        },

        displayRealTimeSuggestions: function(suggestions, $field) {
            // Remove existing real-time suggestions
            $('.real-time-suggestions').remove();
            
            if (suggestions.length === 0) return;
            
            const $suggestionsContainer = $('<div class="real-time-suggestions"></div>');
            
            suggestions.forEach(suggestion => {
                const $suggestionItem = $(`
                    <div class="real-time-suggestion-item" data-suggestion="${suggestion.text}">
                        <span class="suggestion-text">${suggestion.text}</span>
                        <span class="suggestion-type">${suggestion.type}</span>
                    </div>
                `);
                $suggestionsContainer.append($suggestionItem);
            });
            
            // Position suggestions near the field
            $field.after($suggestionsContainer);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                $suggestionsContainer.fadeOut();
            }, 5000);
        },

        loadFieldSuggestions: function(e) {
            const $field = $(e.currentTarget);
            const fieldType = $field.data('ai-type');
            const $widget = $field.siblings('.chatgabi-ai-widget').first();
            
            if (!$widget.length || $widget.hasClass('suggestions-loaded')) return;
            
            const userContext = this.getUserContext();
            
            $.ajax({
                url: chatgabiConfig.restUrl + 'ai-widgets/suggestions',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': chatgabiConfig.restNonce
                },
                data: {
                    field_type: fieldType,
                    field_content: $field.val(),
                    user_context: userContext
                },
                success: function(response) {
                    if (response.success) {
                        AIWidgetManager.updateWidgetSuggestions($widget, response);
                        $widget.addClass('suggestions-loaded');
                    }
                },
                error: function(xhr) {
                    console.log('Field suggestions error:', xhr);
                }
            });
        },

        loadInitialSuggestions: function($widget, fieldType) {
            // Load initial suggestions for the widget
            this.loadFieldSuggestions({ currentTarget: $widget.siblings('.ai-enhanced-field').first() });
        },

        updateWidgetSuggestions: function($widget, response) {
            const $suggestionsContainer = $widget.find('.suggestions-list');
            
            if (response.suggestions && response.suggestions.length > 0) {
                $suggestionsContainer.empty();
                
                response.suggestions.forEach(suggestion => {
                    const $suggestionItem = $(`
                        <div class="suggestion-item">
                            <div class="suggestion-text">${suggestion.text}</div>
                            <div class="suggestion-meta">
                                <span class="suggestion-type">${suggestion.type}</span>
                                ${suggestion.confidence ? `<span class="suggestion-confidence">${suggestion.confidence}% match</span>` : ''}
                            </div>
                        </div>
                    `);
                    $suggestionsContainer.append($suggestionItem);
                });
            }
        },

        getUserContext: function() {
            return {
                country: chatgabiConfig.userCountry || 'GH',
                industry: chatgabiConfig.userIndustry || 'general',
                language: chatgabiConfig.userLanguage || 'en'
            };
        },

        updateCreditsDisplay: function(newCredits) {
            $('.user-credits-display').text(newCredits);
            if (typeof chatgabiConfig !== 'undefined') {
                chatgabiConfig.userCredits = newCredits;
            }
        },

        showMessage: function(message, type = 'info') {
            // Create or update message container
            let $messageContainer = $('.ai-widget-messages');
            if (!$messageContainer.length) {
                $messageContainer = $('<div class="ai-widget-messages"></div>');
                $('body').append($messageContainer);
            }
            
            const $message = $(`
                <div class="ai-widget-message ai-widget-message-${type}">
                    ${message}
                    <button class="message-close">&times;</button>
                </div>
            `);
            
            $messageContainer.append($message);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                $message.fadeOut(() => $message.remove());
            }, 5000);
        }
    };

    // Apply enhancement button handler
    $(document).on('click', '.btn-apply-enhancement', function(e) {
        e.preventDefault();
        
        const enhancedContent = $(this).data('content');
        const $widget = $(this).closest('.chatgabi-ai-widget');
        const $field = $widget.siblings('input, textarea').first();
        
        if ($field.length) {
            $field.val(enhancedContent);
            $field.trigger('change');
            AIWidgetManager.showMessage(__('Enhancement applied successfully!', 'chatgabi'), 'success');
        }
    });

    // Merge enhancement button handler
    $(document).on('click', '.btn-merge-enhancement', function(e) {
        e.preventDefault();
        
        const originalContent = $(this).data('original');
        const enhancedContent = $(this).data('enhanced');
        const $widget = $(this).closest('.chatgabi-ai-widget');
        const $field = $widget.siblings('input, textarea').first();
        
        if ($field.length) {
            const mergedContent = originalContent + '\n\n--- AI Enhanced Version ---\n\n' + enhancedContent;
            $field.val(mergedContent);
            $field.trigger('change');
            AIWidgetManager.showMessage(__('Content merged successfully!', 'chatgabi'), 'success');
        }
    });

    // Real-time suggestion click handler
    $(document).on('click', '.real-time-suggestion-item', function(e) {
        e.preventDefault();
        
        const suggestionText = $(this).data('suggestion');
        const $field = $(this).siblings('input, textarea').first();
        
        if (!$field.length) {
            $field = $(this).parent().siblings('input, textarea').first();
        }
        
        if ($field.length) {
            const currentValue = $field.val();
            const cursorPos = $field[0].selectionStart;
            const newValue = currentValue.substring(0, cursorPos) + suggestionText + currentValue.substring(cursorPos);
            
            $field.val(newValue);
            $field.trigger('change');
            
            // Remove suggestions
            $('.real-time-suggestions').fadeOut();
        }
    });

    // Message close handler
    $(document).on('click', '.message-close', function(e) {
        e.preventDefault();
        $(this).closest('.ai-widget-message').fadeOut(() => $(this).remove());
    });

    // Initialize when document is ready
    $(document).ready(function() {
        AIWidgetManager.init();
    });

    // Expose to global scope for external access
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.AIWidgetManager = AIWidgetManager;

})(jQuery);
