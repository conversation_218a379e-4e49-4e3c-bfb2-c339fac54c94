<?php
/**
 * Enhanced Business Template Translation System for ChatGABI
 * 
 * This file provides comprehensive template translation with African cultural context,
 * local business terminology, and country-specific business intelligence.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_Enhanced_Template_Translator {
    
    private $african_context_engine;
    private $supported_languages;
    private $business_terminology;
    private $cultural_contexts;
    
    public function __construct() {
        $this->african_context_engine = new BusinessCraft_African_Context_Engine();
        $this->init_supported_languages();
        $this->init_business_terminology();
        $this->init_cultural_contexts();
    }
    
    /**
     * Initialize supported languages with cultural metadata
     */
    private function init_supported_languages() {
        $this->supported_languages = array(
            'en' => array(
                'name' => __('English', 'chatgabi'),
                'native_name' => 'English',
                'countries' => array('US', 'GB', 'AU', 'CA'),
                'business_formality' => 'professional',
                'cultural_context' => 'international_business'
            ),
            'sw' => array(
                'name' => __('Swahili', 'chatgabi'),
                'native_name' => 'Kiswahili',
                'countries' => array('KE', 'TZ', 'UG'),
                'business_formality' => 'respectful_innovative',
                'cultural_context' => 'east_african_business'
            ),
            'yo' => array(
                'name' => __('Yoruba', 'chatgabi'),
                'native_name' => 'Yorùbá',
                'countries' => array('NG', 'BJ'),
                'business_formality' => 'ambitious_hierarchical',
                'cultural_context' => 'west_african_business'
            ),
            'tw' => array(
                'name' => __('Twi', 'chatgabi'),
                'native_name' => 'Twi',
                'countries' => array('GH'),
                'business_formality' => 'community_oriented',
                'cultural_context' => 'akan_business_culture'
            ),
            'zu' => array(
                'name' => __('Zulu', 'chatgabi'),
                'native_name' => 'IsiZulu',
                'countries' => array('ZA'),
                'business_formality' => 'ubuntu_professional',
                'cultural_context' => 'ubuntu_business_culture'
            )
        );
    }
    
    /**
     * Initialize business terminology for each language/culture
     */
    private function init_business_terminology() {
        $this->business_terminology = array(
            'en' => array(
                'business' => 'business',
                'entrepreneur' => 'entrepreneur',
                'startup' => 'startup',
                'market' => 'market',
                'customers' => 'customers',
                'profit' => 'profit',
                'strategy' => 'strategy',
                'community' => 'community',
                'partnership' => 'partnership',
                'innovation' => 'innovation',
                'growth' => 'growth',
                'success' => 'success',
                'opportunity' => 'opportunity',
                'investment' => 'investment',
                'revenue' => 'revenue',
                'competition' => 'competition',
                'leadership' => 'leadership',
                'sustainability' => 'sustainability',
                'digital_transformation' => 'digital transformation',
                'market_research' => 'market research'
            ),
            'sw' => array(
                'business' => 'biashara',
                'entrepreneur' => 'mjasiriamali',
                'startup' => 'biashara mpya',
                'market' => 'soko',
                'customers' => 'wateja',
                'profit' => 'faida',
                'strategy' => 'mkakati',
                'community' => 'jamii',
                'partnership' => 'ushirikiano',
                'innovation' => 'uvumbuzi',
                'growth' => 'ukuaji',
                'success' => 'mafanikio',
                'opportunity' => 'fursa',
                'investment' => 'uwekezaji',
                'revenue' => 'mapato',
                'competition' => 'ushindani',
                'leadership' => 'uongozi',
                'sustainability' => 'uendelevu',
                'digital_transformation' => 'mabadiliko ya kidijitali',
                'market_research' => 'utafiti wa soko'
            ),
            'yo' => array(
                'business' => 'iṣowo',
                'entrepreneur' => 'oṣowo',
                'startup' => 'iṣowo tuntun',
                'market' => 'ọja',
                'customers' => 'awọn alabara',
                'profit' => 'ere',
                'strategy' => 'ọgbọn',
                'community' => 'agbegbe',
                'partnership' => 'ajọṣepọ',
                'innovation' => 'imudara',
                'growth' => 'idagbasoke',
                'success' => 'aṣeyọri',
                'opportunity' => 'aye',
                'investment' => 'idoko-owo',
                'revenue' => 'owo wiwọle',
                'competition' => 'idije',
                'leadership' => 'aṣaju',
                'sustainability' => 'aifọwọyi',
                'digital_transformation' => 'iyipada oni-nọmba',
                'market_research' => 'iwadi ọja'
            ),
            'tw' => array(
                'business' => 'adwuma',
                'entrepreneur' => 'adwumawura',
                'startup' => 'adwuma foforo',
                'market' => 'gua',
                'customers' => 'adetɔfo',
                'profit' => 'mfaso',
                'strategy' => 'nhyehyɛe',
                'community' => 'mpɔtam',
                'partnership' => 'nkabom',
                'innovation' => 'nneɛma foforo',
                'growth' => 'nkɔso',
                'success' => 'nkonimdi',
                'opportunity' => 'hokwan',
                'investment' => 'sika a wɔde ma',
                'revenue' => 'sika a wɔnya',
                'competition' => 'akansi',
                'leadership' => 'akannifo',
                'sustainability' => 'ntoaso',
                'digital_transformation' => 'dijitaal nsakrae',
                'market_research' => 'gua mu nhwehwɛmu'
            ),
            'zu' => array(
                'business' => 'ibhizinisi',
                'entrepreneur' => 'usomabhizinisi',
                'startup' => 'ibhizinisi elisha',
                'market' => 'imakethe',
                'customers' => 'amakhasimende',
                'profit' => 'inzuzo',
                'strategy' => 'isu',
                'community' => 'umphakathi',
                'partnership' => 'ubambiswano',
                'innovation' => 'ukusungula',
                'growth' => 'ukukhula',
                'success' => 'impumelelo',
                'opportunity' => 'ithuba',
                'investment' => 'utshalomali',
                'revenue' => 'imali engenayo',
                'competition' => 'ukuncintisana',
                'leadership' => 'ubuholi',
                'sustainability' => 'ukusimama',
                'digital_transformation' => 'ukuguquka kwedijithali',
                'market_research' => 'ucwaningo lwemakethe'
            )
        );
    }
    
    /**
     * Initialize cultural contexts for each language
     */
    private function init_cultural_contexts() {
        $this->cultural_contexts = array(
            'sw' => array(
                'business_philosophy' => 'Harambee (collective effort) and innovation-driven approach',
                'communication_style' => 'Direct, practical, efficiency-focused with professional respect',
                'decision_making' => 'Consensus-building with emphasis on practical outcomes',
                'relationship_building' => 'Professional networks, tech communities, and innovation hubs',
                'success_metrics' => 'Innovation impact, community benefit, and scalable growth',
                'cultural_values' => array('innovation', 'efficiency', 'community_progress', 'education'),
                'business_greetings' => array('Hujambo', 'Habari za biashara', 'Karibu'),
                'respect_terms' => array('Mzee', 'Dada', 'Kaka', 'Mama', 'Baba')
            ),
            'yo' => array(
                'business_philosophy' => 'Omolúàbí (good character) and entrepreneurial excellence',
                'communication_style' => 'Respectful hierarchy with bold vision articulation',
                'decision_making' => 'Elder consultation with ambitious goal-setting',
                'relationship_building' => 'Extended family networks and community solidarity',
                'success_metrics' => 'Wealth creation, family advancement, and community impact',
                'cultural_values' => array('respect', 'ambition', 'family_success', 'community_honor'),
                'business_greetings' => array('Ẹ kú àárọ̀', 'Ẹ kú ọ̀sán', 'Báwo ni iṣẹ́'),
                'respect_terms' => array('Baba', 'Mama', 'Ẹgbọ́n', 'Àgbà')
            ),
            'tw' => array(
                'business_philosophy' => 'Sankofa (learning from the past) and community prosperity',
                'communication_style' => 'Respectful, community-oriented with traditional wisdom',
                'decision_making' => 'Community consensus with elder guidance',
                'relationship_building' => 'Extended family networks and traditional authority respect',
                'success_metrics' => 'Community benefit, traditional value preservation, sustainable growth',
                'cultural_values' => array('respect', 'community', 'tradition', 'collective_prosperity'),
                'business_greetings' => array('Maakye', 'Maaha', 'Wo ho te sɛn'),
                'respect_terms' => array('Nana', 'Opanin', 'Maame', 'Papa')
            ),
            'zu' => array(
                'business_philosophy' => 'Ubuntu (interconnectedness) and professional excellence',
                'communication_style' => 'Ubuntu-based with professional standards and quality focus',
                'decision_making' => 'Consensus-building with community welfare consideration',
                'relationship_building' => 'Strong community bonds and professional networks',
                'success_metrics' => 'Community upliftment, quality standards, and sustainable impact',
                'cultural_values' => array('ubuntu', 'quality', 'community_upliftment', 'professional_excellence'),
                'business_greetings' => array('Sawubona', 'Sanibonani', 'Kunjani'),
                'respect_terms' => array('Baba', 'Mama', 'Mkhulu', 'Gogo')
            )
        );
    }
    
    /**
     * Translate business template with cultural context
     */
    public function translate_template($template_content, $target_language, $country_code, $industry = null) {
        // Get language and cultural context
        $language_context = $this->supported_languages[$target_language] ?? $this->supported_languages['en'];
        $cultural_context = $this->cultural_contexts[$target_language] ?? array();
        $terminology = $this->business_terminology[$target_language] ?? $this->business_terminology['en'];
        
        // Get African context for the country
        $african_context = $this->african_context_engine->get_country_context($country_code);
        
        // Start with base translation
        $translated_content = $this->apply_terminology_translation($template_content, $terminology);
        
        // Add cultural context
        $translated_content = $this->add_cultural_context($translated_content, $cultural_context, $country_code);
        
        // Add country-specific business intelligence
        $translated_content = $this->add_country_specific_context($translated_content, $african_context, $country_code);
        
        // Add industry-specific context if provided
        if ($industry) {
            $translated_content = $this->add_industry_context($translated_content, $industry, $country_code, $target_language);
        }
        
        return $translated_content;
    }
    
    /**
     * Apply terminology translation to content
     */
    private function apply_terminology_translation($content, $terminology) {
        foreach ($terminology as $english_term => $local_term) {
            // Replace whole words only
            $content = preg_replace('/\b' . preg_quote($english_term, '/') . '\b/i', $local_term, $content);
        }
        
        return $content;
    }
    
    /**
     * Add cultural context to translated content
     */
    private function add_cultural_context($content, $cultural_context, $country_code) {
        if (empty($cultural_context)) {
            return $content;
        }
        
        // Add cultural philosophy
        if (isset($cultural_context['business_philosophy'])) {
            $content .= "\n\n**" . __('Cultural Business Philosophy', 'chatgabi') . "**: " . $cultural_context['business_philosophy'];
        }
        
        // Add communication style guidance
        if (isset($cultural_context['communication_style'])) {
            $content .= "\n\n**" . __('Communication Style', 'chatgabi') . "**: " . $cultural_context['communication_style'];
        }
        
        // Add cultural values
        if (isset($cultural_context['cultural_values'])) {
            $values = implode(', ', $cultural_context['cultural_values']);
            $content .= "\n\n**" . __('Key Cultural Values', 'chatgabi') . "**: " . $values;
        }
        
        return $content;
    }
    
    /**
     * Add country-specific business context
     */
    private function add_country_specific_context($content, $african_context, $country_code) {
        $country_name = $this->get_country_name($country_code);
        
        // Add regulatory environment
        if (isset($african_context['regulatory_environment'])) {
            $content .= "\n\n**" . sprintf(__('%s Regulatory Environment', 'chatgabi'), $country_name) . "**: " . $african_context['regulatory_environment'];
        }
        
        // Add payment preferences
        if (isset($african_context['payment_preferences'])) {
            $content .= "\n\n**" . sprintf(__('%s Payment Methods', 'chatgabi'), $country_name) . "**: " . $african_context['payment_preferences'];
        }
        
        // Add market characteristics
        if (isset($african_context['market_characteristics'])) {
            $content .= "\n\n**" . sprintf(__('%s Market Characteristics', 'chatgabi'), $country_name) . "**: " . $african_context['market_characteristics'];
        }
        
        // Add business challenges
        if (isset($african_context['business_challenges'])) {
            $content .= "\n\n**" . sprintf(__('Common Business Challenges in %s', 'chatgabi'), $country_name) . "**: " . $african_context['business_challenges'];
        }
        
        // Add opportunities
        if (isset($african_context['opportunities'])) {
            $content .= "\n\n**" . sprintf(__('Business Opportunities in %s', 'chatgabi'), $country_name) . "**: " . $african_context['opportunities'];
        }
        
        return $content;
    }
    
    /**
     * Add industry-specific context
     */
    private function add_industry_context($content, $industry, $country_code, $language) {
        // Get industry-specific examples from African Context Engine
        $market_examples = $this->african_context_engine->generate_market_examples($country_code, 'industry_specific');
        
        if (isset($market_examples['successful_businesses'])) {
            $content .= "\n\n**" . sprintf(__('Successful %s Businesses', 'chatgabi'), ucfirst($industry)) . "**: ";
            
            // Add relevant business examples
            if (isset($market_examples['successful_businesses']['tech_startups']) && $industry === 'technology') {
                $content .= $market_examples['successful_businesses']['tech_startups'];
            } elseif (isset($market_examples['successful_businesses']['sme_success_stories'])) {
                $content .= $market_examples['successful_businesses']['sme_success_stories'];
            }
        }
        
        return $content;
    }
    
    /**
     * Get country name from code
     */
    private function get_country_name($country_code) {
        $countries = array(
            'GH' => __('Ghana', 'chatgabi'),
            'KE' => __('Kenya', 'chatgabi'),
            'NG' => __('Nigeria', 'chatgabi'),
            'ZA' => __('South Africa', 'chatgabi')
        );
        return $countries[$country_code] ?? __('Ghana', 'chatgabi');
    }
    
    /**
     * Get enhanced template with full cultural context
     */
    public function get_enhanced_template($template_id, $language, $country_code, $industry = null) {
        // Get base template
        $template = $this->get_base_template($template_id);
        
        if (!$template) {
            return null;
        }
        
        // Translate and enhance
        $enhanced_template = array(
            'id' => $template['id'],
            'title' => $this->translate_template($template['title'], $language, $country_code, $industry),
            'description' => $this->translate_template($template['description'], $language, $country_code, $industry),
            'content' => $this->translate_template($template['content'], $language, $country_code, $industry),
            'language' => $language,
            'country_code' => $country_code,
            'industry' => $industry,
            'cultural_context' => $this->cultural_contexts[$language] ?? array(),
            'business_terminology' => $this->business_terminology[$language] ?? array(),
            'local_examples' => $this->african_context_engine->generate_market_examples($country_code, 'template_specific')
        );
        
        return $enhanced_template;
    }
    
    /**
     * Get base template from database or file system
     */
    private function get_base_template($template_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d",
            $template_id
        ), ARRAY_A);
        
        return $template;
    }
}

// Initialize the enhanced template translator
function chatgabi_get_enhanced_template_translator() {
    static $translator = null;
    
    if ($translator === null) {
        $translator = new ChatGABI_Enhanced_Template_Translator();
    }
    
    return $translator;
}
?>
