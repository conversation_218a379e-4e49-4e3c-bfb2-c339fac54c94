/**
 * AI Template Widgets Styles
 * Embedded AI assistance for template fields
 */

/* AI Widget Container */
.chatgabi-ai-widget {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    margin: 15px 0;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chatgabi-ai-widget:hover {
    border-color: #007cba;
    box-shadow: 0 4px 16px rgba(0, 124, 186, 0.1);
}

/* Widget Header */
.ai-widget-header {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    user-select: none;
}

.ai-widget-header:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%);
}

.ai-widget-icon {
    font-size: 1.2rem;
    margin-right: 8px;
}

.ai-widget-title {
    font-weight: 600;
    font-size: 1rem;
    flex: 1;
}

.ai-widget-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.ai-widget-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.toggle-icon {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.ai-widget.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

/* Widget Content */
.ai-widget-content {
    padding: 20px;
    display: block;
    transition: all 0.3s ease;
}

.ai-widget.collapsed .ai-widget-content {
    display: none;
}

/* Field Suggestions */
.field-suggestions {
    margin-bottom: 20px;
}

.field-suggestions h4 {
    color: #333;
    font-size: 1.1rem;
    margin: 0 0 15px 0;
    font-weight: 600;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.suggestion-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.suggestion-item:hover {
    border-color: #007cba;
    background: #f0f8ff;
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(0, 124, 186, 0.1);
}

.suggestion-text {
    font-size: 0.95rem;
    color: #333;
    line-height: 1.5;
    margin-bottom: 8px;
}

.suggestion-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
}

.suggestion-type {
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.suggestion-confidence {
    color: #28a745;
    font-weight: 500;
}

.no-suggestions {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px dashed #dee2e6;
}

/* AI Enhancement Options */
.ai-enhancement-options {
    margin-bottom: 20px;
}

.ai-enhancement-options h4 {
    color: #333;
    font-size: 1.1rem;
    margin: 0 0 15px 0;
    font-weight: 600;
}

.enhancement-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.ai-enhance-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.ai-enhance-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.ai-enhance-btn:active {
    transform: translateY(0);
}

.ai-enhance-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* AI Suggestions Display */
.ai-suggestions-display {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    min-height: 100px;
}

.suggestions-placeholder {
    text-align: center;
    color: #666;
    font-style: italic;
}

.ai-suggestion-result {
    background: #f0f8ff;
    border-left: 4px solid #007cba;
    padding: 15px;
    border-radius: 0 8px 8px 0;
    margin-bottom: 15px;
}

.suggestion-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.suggestion-result-title {
    font-weight: 600;
    color: #007cba;
    font-size: 1rem;
}

.suggestion-result-actions {
    display: flex;
    gap: 8px;
}

.apply-suggestion-btn,
.copy-suggestion-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-suggestion-btn:hover,
.copy-suggestion-btn:hover {
    background: #005a87;
    transform: translateY(-1px);
}

/* Real-time Suggestions */
.real-time-suggestions {
    position: absolute;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 300px;
    margin-top: 5px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.real-time-suggestion-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.real-time-suggestion-item:last-child {
    border-bottom: none;
}

.real-time-suggestion-item:hover {
    background: #f8f9fa;
}

.real-time-suggestion-item .suggestion-text {
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 4px;
}

.real-time-suggestion-item .suggestion-type {
    font-size: 0.75rem;
    color: #666;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 10px;
}

/* Enhanced Enhancement Result */
.enhancement-result {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.enhancement-result h5 {
    color: #28a745;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.enhancement-result h5::before {
    content: '✨';
    font-size: 18px;
}

.enhanced-content {
    background: #f8f9fa;
    border-left: 4px solid #28a745;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 0 8px 8px 0;
    line-height: 1.6;
    white-space: pre-wrap;
}

.enhancement-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.btn-apply-enhancement,
.btn-merge-enhancement {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-apply-enhancement {
    background: #28a745;
    color: white;
}

.btn-apply-enhancement:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-merge-enhancement {
    background: #6c757d;
    color: white;
}

.btn-merge-enhancement:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.enhancement-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.credits-used {
    color: #dc3545;
    font-weight: 600;
}

.remaining-credits {
    color: #28a745;
    font-weight: 600;
}

/* AI Widget Footer */
.ai-widget-footer {
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid #e1e8f5;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.credit-cost-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.cost-label {
    color: #6c757d;
    font-weight: 500;
}

.cost-amount {
    background: #ffc107;
    color: #212529;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

/* Loading States */
.ai-enhance-btn .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Applied Suggestion Feedback */
.suggestion-item.suggestion-applied {
    background: #d4edda;
    border-color: #28a745;
    animation: appliedFeedback 0.5s ease;
}

@keyframes appliedFeedback {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* AI Widget Messages */
.ai-widget-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.ai-widget-message {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

.ai-widget-message-success {
    border-left: 4px solid #28a745;
}

.ai-widget-message-error {
    border-left: 4px solid #dc3545;
}

.ai-widget-message-warning {
    border-left: 4px solid #ffc107;
}

.ai-widget-message-info {
    border-left: 4px solid #007bff;
}

.message-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    margin-left: 15px;
}

.message-close:hover {
    color: #333;
}

.apply-suggestion-btn:hover,
.copy-suggestion-btn:hover {
    background: #005a87;
}

.suggestion-result-content {
    color: #333;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Widget Footer */
.ai-widget-footer {
    background: #f8f9fa;
    padding: 12px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.credit-cost-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.cost-label {
    color: #666;
    font-weight: 500;
}

.cost-amount {
    background: #ffc107;
    color: #333;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Loading States */
.ai-widget-loading {
    position: relative;
    pointer-events: none;
}

.ai-widget-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
}

.ai-widget-loading::before {
    content: '🤖 AI is thinking...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-weight: 500;
    color: #333;
    z-index: 10;
}

/* Enhancement Result Animation */
.enhancement-result-enter {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.5s ease forwards;
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-widget-header {
        padding: 10px 15px;
    }
    
    .ai-widget-content {
        padding: 15px;
    }
    
    .enhancement-buttons {
        flex-direction: column;
    }
    
    .ai-enhance-btn {
        width: 100%;
        justify-content: center;
    }
    
    .suggestion-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .suggestion-result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .suggestion-result-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .chatgabi-ai-widget {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .ai-widget-content {
        background: #2d3748;
    }
    
    .field-suggestions h4,
    .ai-enhancement-options h4 {
        color: #e2e8f0;
    }
    
    .suggestion-item {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .suggestion-item:hover {
        background: #2b6cb0;
        border-color: #3182ce;
    }
    
    .ai-suggestions-display {
        background: #4a5568;
        border-color: #718096;
    }
    
    .suggestions-placeholder {
        color: #a0aec0;
    }
}
