/**
 * ChatGABI Language Switcher Styles
 * 
 * Responsive and accessible language switching interface
 */

/* Base Language Switcher Styles */
.chatgabi-language-switcher {
    position: relative;
    display: inline-block;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
}

.chatgabi-language-switcher.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Dropdown Style */
.chatgabi-language-select {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.chatgabi-language-select:hover {
    border-color: #007cba;
}

.chatgabi-language-select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* Button Style */
.chatgabi-language-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.chatgabi-language-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.chatgabi-language-btn:hover {
    background: #e9ecef;
    border-color: #007cba;
    color: #007cba;
}

.chatgabi-language-btn.active {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
}

.chatgabi-language-btn .flag {
    font-size: 16px;
    line-height: 1;
}

.chatgabi-language-btn .name {
    font-weight: 500;
}

/* Minimal Style */
.chatgabi-language-minimal {
    position: relative;
    display: inline-block;
}

.chatgabi-language-minimal .current-language {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    position: relative;
}

.chatgabi-language-minimal .current-language:after {
    content: '▼';
    font-size: 10px;
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.chatgabi-language-minimal .current-language:hover {
    background: #e9ecef;
    border-color: #007cba;
}

.chatgabi-language-minimal .language-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 4px;
}

.chatgabi-language-minimal .language-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.chatgabi-language-minimal .language-option {
    display: block;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.chatgabi-language-minimal .language-option:last-child {
    border-bottom: none;
}

.chatgabi-language-minimal .language-option:hover {
    background: #f8f9fa;
    color: #007cba;
}

/* Header Integration */
.header-language-switcher {
    display: inline-block;
    margin-left: auto;
}

.header-language-switcher .chatgabi-language-switcher {
    font-size: 13px;
}

.header-language-switcher .chatgabi-language-select,
.header-language-switcher .current-language {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 6px 10px;
}

.header-language-switcher .chatgabi-language-select:hover,
.header-language-switcher .current-language:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
}

/* Message Display */
.chatgabi-language-message {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    margin-top: 4px;
    z-index: 1001;
    display: none;
}

.chatgabi-language-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.chatgabi-language-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.chatgabi-language-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Loading State */
.chatgabi-language-switcher.loading:before {
    content: '';
    position: absolute;
    top: 50%;
    right: 8px;
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: chatgabi-spin 1s linear infinite;
    z-index: 10;
}

@keyframes chatgabi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatgabi-language-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .chatgabi-language-btn {
        justify-content: center;
        padding: 10px 12px;
    }
    
    .chatgabi-language-select {
        width: 100%;
        min-width: auto;
    }
    
    .header-language-switcher {
        margin: 0;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .chatgabi-language-switcher {
        font-size: 13px;
    }
    
    .chatgabi-language-select,
    .current-language,
    .chatgabi-language-btn {
        padding: 6px 8px;
        font-size: 13px;
    }
    
    .chatgabi-language-btn .name {
        display: none;
    }
    
    .chatgabi-language-btn .flag {
        font-size: 18px;
    }
}

/* RTL Support */
[dir="rtl"] .chatgabi-language-minimal .current-language:after {
    margin-left: 0;
    margin-right: 8px;
}

[dir="rtl"] .chatgabi-language-minimal .language-dropdown {
    left: auto;
    right: 0;
}

[dir="rtl"] .header-language-switcher {
    margin-left: 0;
    margin-right: auto;
}

/* Accessibility */
.chatgabi-language-switcher:focus-within {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.chatgabi-language-btn:focus,
.chatgabi-language-select:focus,
.current-language:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .chatgabi-language-select,
    .chatgabi-language-btn,
    .current-language {
        border-width: 2px;
    }
    
    .chatgabi-language-btn.active {
        background: #000;
        color: #fff;
        border-color: #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .chatgabi-language-select,
    .chatgabi-language-btn,
    .current-language,
    .language-dropdown {
        transition: none;
    }
    
    .chatgabi-language-switcher.loading:before {
        animation: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .chatgabi-language-select,
    .chatgabi-language-btn,
    .current-language {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .chatgabi-language-select:hover,
    .chatgabi-language-btn:hover,
    .current-language:hover {
        background: #4a5568;
        border-color: #63b3ed;
    }
    
    .chatgabi-language-btn.active {
        background: #3182ce;
        border-color: #3182ce;
    }
    
    .language-dropdown {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .language-option:hover {
        background: #4a5568;
        color: #63b3ed;
    }
}
