<?php
/**
 * Final ChatGABI AI Widget Endpoints Status Report
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Load WordPress
require_once('../../../wp-load.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI AI Widget Endpoints - Final Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .status-pass { background-color: #d4edda; color: #155724; border-color: #c3e6cb; }
        .status-fail { background-color: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .status-warning { background-color: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .status-info { background-color: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .endpoint-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007cba; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #005a87; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .summary-number { font-size: 2em; font-weight: bold; color: #007cba; }
    </style>
</head>
<body>

<div class="container">
    <h1>🚀 ChatGABI AI Widget Endpoints - Final Status Report</h1>
    <p><strong>Date:</strong> <?php echo current_time('F j, Y g:i A'); ?></p>
    
    <?php
    // Get REST server and routes
    $rest_server = rest_get_server();
    $routes = $rest_server->get_routes();
    
    // Count ChatGABI routes
    $chatgabi_routes = 0;
    $ai_widget_routes = 0;
    foreach ($routes as $route => $data) {
        if (strpos($route, '/chatgabi/v1/') !== false) {
            $chatgabi_routes++;
            if (strpos($route, '/ai-widgets/') !== false) {
                $ai_widget_routes++;
            }
        }
    }
    
    // Check functions
    $required_functions = array(
        'chatgabi_get_ai_field_suggestions',
        'chatgabi_enhance_field_content',
        'chatgabi_get_real_time_suggestions',
        'chatgabi_check_user_permission'
    );
    
    $functions_available = 0;
    foreach ($required_functions as $func) {
        if (function_exists($func)) {
            $functions_available++;
        }
    }
    
    // Check AI widget endpoints specifically
    $ai_endpoints = array(
        '/chatgabi/v1/ai-widgets/suggestions',
        '/chatgabi/v1/ai-widgets/enhance',
        '/chatgabi/v1/ai-widgets/real-time-suggestions'
    );
    
    $endpoints_registered = 0;
    foreach ($ai_endpoints as $endpoint) {
        if (isset($routes[$endpoint])) {
            $endpoints_registered++;
        }
    }
    ?>
    
    <!-- Summary Cards -->
    <div class="summary-grid">
        <div class="summary-card">
            <div class="summary-number"><?php echo $endpoints_registered; ?>/3</div>
            <div>AI Widget Endpoints</div>
        </div>
        <div class="summary-card">
            <div class="summary-number"><?php echo $functions_available; ?>/4</div>
            <div>Required Functions</div>
        </div>
        <div class="summary-card">
            <div class="summary-number"><?php echo $chatgabi_routes; ?></div>
            <div>Total ChatGABI Routes</div>
        </div>
        <div class="summary-card">
            <div class="summary-number"><?php echo count($routes); ?></div>
            <div>Total REST Routes</div>
        </div>
    </div>
    
    <!-- Overall Status -->
    <div class="status-section <?php echo ($endpoints_registered === 3 && $functions_available === 4) ? 'status-pass' : 'status-warning'; ?>">
        <h2>🎯 Overall Status</h2>
        <?php if ($endpoints_registered === 3 && $functions_available === 4): ?>
            <p><strong>✅ SUCCESS:</strong> All AI widget endpoints are registered and functional!</p>
        <?php else: ?>
            <p><strong>⚠️ PARTIAL:</strong> Some components may need attention.</p>
        <?php endif; ?>
    </div>
    
    <!-- Endpoint Details -->
    <div class="status-section">
        <h2>🔗 AI Widget Endpoints Status</h2>
        
        <?php foreach ($ai_endpoints as $endpoint): ?>
            <div class="endpoint-card">
                <h3><?php echo $endpoint; ?></h3>
                <?php if (isset($routes[$endpoint])): ?>
                    <p><strong>✅ Status:</strong> Registered and Available</p>
                    <?php 
                    $route_data = $routes[$endpoint][0];
                    $callback = $route_data['callback'];
                    $permission = $route_data['permission_callback'];
                    ?>
                    <p><strong>Callback:</strong> <?php echo $callback; ?> 
                        <?php echo function_exists($callback) ? '✅' : '❌'; ?>
                    </p>
                    <p><strong>Permission:</strong> <?php echo $permission; ?>
                        <?php echo function_exists($permission) ? '✅' : '❌'; ?>
                    </p>
                    <p><strong>Methods:</strong> <?php echo implode(', ', array_keys($route_data['methods'])); ?></p>
                <?php else: ?>
                    <p><strong>❌ Status:</strong> Not Registered</p>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Function Status -->
    <div class="status-section">
        <h2>⚙️ Function Availability</h2>
        
        <?php foreach ($required_functions as $func): ?>
            <div class="endpoint-card">
                <h4><?php echo $func; ?></h4>
                <?php if (function_exists($func)): ?>
                    <p><strong>✅ Available</strong> - Function is loaded and callable</p>
                <?php else: ?>
                    <p><strong>❌ Missing</strong> - Function is not available</p>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Test Section -->
    <div class="status-section">
        <h2>🧪 Live Testing</h2>
        <p>Test the endpoints with actual HTTP requests:</p>
        
        <div id="test-results"></div>
        
        <button class="test-button" onclick="testEndpoint('suggestions')">Test Suggestions Endpoint</button>
        <button class="test-button" onclick="testEndpoint('enhance')">Test Enhancement Endpoint</button>
        <button class="test-button" onclick="testEndpoint('real-time')">Test Real-time Endpoint</button>
    </div>
    
    <!-- Implementation Status -->
    <div class="status-section status-info">
        <h2>📋 Implementation Summary</h2>
        <h3>✅ Completed:</h3>
        <ul>
            <li>Fixed duplicate endpoint registrations</li>
            <li>Resolved conditional loading conflicts</li>
            <li>Added timeout protection to real-time suggestions</li>
            <li>Implemented safe helper functions</li>
            <li>Enhanced error handling and logging</li>
            <li>Verified REST API route registration</li>
        </ul>
        
        <h3>🎯 Ready for Use:</h3>
        <ul>
            <li>AI field suggestions with African market context</li>
            <li>Content enhancement with credit deduction</li>
            <li>Real-time typing suggestions</li>
            <li>User authentication and permission checks</li>
            <li>Comprehensive error handling</li>
        </ul>
    </div>
    
    <!-- Next Steps -->
    <div class="status-section status-info">
        <h2>🚀 Next Steps</h2>
        <ol>
            <li><strong>Frontend Integration:</strong> Ensure JavaScript widgets can communicate with these endpoints</li>
            <li><strong>User Testing:</strong> Test with logged-in users to verify authentication flow</li>
            <li><strong>Performance Monitoring:</strong> Monitor endpoint response times and error rates</li>
            <li><strong>Credit System:</strong> Verify credit deduction works correctly</li>
            <li><strong>AI Integration:</strong> Test OpenAI API integration for enhanced suggestions</li>
        </ol>
    </div>
</div>

<script>
function testEndpoint(type) {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<p>Testing ' + type + ' endpoint...</p>';
    
    let endpoint, data;
    
    switch(type) {
        case 'suggestions':
            endpoint = '<?php echo rest_url('chatgabi/v1/ai-widgets/suggestions'); ?>';
            data = {
                field_type: 'business_description',
                field_content: 'Test business content',
                user_context: {country: 'GH', industry: 'technology'}
            };
            break;
        case 'enhance':
            endpoint = '<?php echo rest_url('chatgabi/v1/ai-widgets/enhance'); ?>';
            data = {
                field_id: 'test-field',
                action: 'improve',
                current_content: 'Test content to enhance',
                user_context: {country: 'GH', industry: 'technology'}
            };
            break;
        case 'real-time':
            endpoint = '<?php echo rest_url('chatgabi/v1/ai-widgets/real-time-suggestions'); ?>';
            data = {
                field_type: 'business_description',
                partial_content: 'My business provides technology solutions',
                user_context: {country: 'GH', industry: 'technology'}
            };
            break;
    }
    
    fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        resultDiv.innerHTML = '<h4>Test Result for ' + type + ':</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        resultDiv.innerHTML = '<h4>Test Error for ' + type + ':</h4><pre>' + error.toString() + '</pre>';
    });
}
</script>

</body>
</html>
